@echo off
chcp 65001 > nul
echo ================================
echo 🌟 开始构建 Maven 项目（兼容 mvnd）...
echo ================================

rem 切换到项目根目录（脚本的上一级目录）
cd /d %~dp0..
echo 当前路径为：%cd%
echo.

rem 判断 mvnd.cmd 是否存在（优先使用）
set MVN_CMD=mvnd.cmd

where %MVN_CMD% > nul 2>&1
if errorlevel 1 (
    set MVN_CMD=mvn
)

rem 运行编译指令
call %MVN_CMD% clean package

rem 获取执行结果
set "RESULT=%errorlevel%"
echo.

if "%RESULT%"=="0" (
    echo ✅ 构建成功！JAR 文件位于 target\ 目录中。
) else (
    echo ❌ 构建失败，错误代码：%RESULT%
)

echo.