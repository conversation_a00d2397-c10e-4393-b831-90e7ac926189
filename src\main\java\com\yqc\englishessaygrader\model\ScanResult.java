package com.yqc.englishessaygrader.model;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 扫描结果类
 * 用于存储扫描操作的结果信息
 */
public class ScanResult {
    private boolean success;
    private String errorMessage;
    private List<File> scannedFiles;
    private boolean noMorePaper;
    
    public ScanResult() {
        this.success = false;
        this.errorMessage = "";
        this.scannedFiles = new ArrayList<>();
        this.noMorePaper = false;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public List<File> getScannedFiles() {
        return scannedFiles;
    }
    
    public void setScannedFiles(List<File> scannedFiles) {
        this.scannedFiles = scannedFiles;
    }
    
    public void addScannedFile(File file) {
        if (file != null && file.exists()) {
            this.scannedFiles.add(file);
        }
    }
    
    public int getFileCount() {
        return scannedFiles.size();
    }
    
    public boolean isNoMorePaper() {
        return noMorePaper;
    }
    
    public void setNoMorePaper(boolean noMorePaper) {
        this.noMorePaper = noMorePaper;
    }
}