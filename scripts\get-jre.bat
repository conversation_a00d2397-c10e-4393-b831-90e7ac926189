@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

rem 切换到项目根目录（脚本的上一级目录）
cd /d %~dp0..
echo 当前路径为：%cd%
echo.

:: 配置版本和平台
set "JRE_VERSION=jdk-17.0.9%%2B9.1"
set "JRE_NAME=OpenJDK17U-jre_x86-32_windows_hotspot_17.0.9_9"
set "JRE_ZIP=%JRE_NAME%.zip"
set "JRE_URL=https://github.com/adoptium/temurin17-binaries/releases/download/%JRE_VERSION%/%JRE_ZIP%"
set "TARGET_DIR=%~dp0../runtime"
set "JRE_DIR=%TARGET_DIR%\jre"

:: 创建下载目录
mkdir "%TARGET_DIR%" >nul 2>&1
cd /d "%TARGET_DIR%"

:: 如果 JRE 已存在，跳过下载
if exist "%JRE_DIR%\bin\java.exe" (
    echo ✅ JRE 已存在：%JRE_DIR%
    goto end
)

:: 下载 JRE ZIP 文件
echo 📥 正在下载 JRE：%JRE_NAME%
curl -L -o "%JRE_ZIP%" "%JRE_URL%"
if not exist "%JRE_ZIP%" (
    echo ❌ 下载失败，文件未找到：%JRE_ZIP%
    goto end
)

:: 解压 ZIP
echo 📂 正在解压...
powershell -Command "Expand-Archive -Path '%JRE_ZIP%' -DestinationPath '.'" 2>nul

:: 查找解压目录（假设唯一）
for /d %%D in (*) do (
    if exist "%%D\bin\java.exe" (
        set "EXTRACTED_DIR=%%D"
    )
)

:: 判断是否找到有效 JRE
if not defined EXTRACTED_DIR (
    echo ❌ 解压后未找到 JRE 目录。
    goto end
)

:: 移动并重命名 JRE 目录
move "!EXTRACTED_DIR!" "%JRE_DIR%" >nul 2>&1
del "%JRE_ZIP%"

:end
echo.
if exist "%JRE_DIR%\bin\java.exe" (
    echo ✅ JRE 准备完成！路径：%JRE_DIR%
) else (
    echo ❌ JRE 下载或解压失败。
)

endlocal
