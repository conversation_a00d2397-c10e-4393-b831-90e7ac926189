@echo off
chcp 65001 > nul
echo =====================================
echo 🚧 开始执行完整构建流程...
echo =====================================

rem 切换到项目根目录
cd /d %~dp0

rem 版本号来源：参数或交互输入
set /p VERSION=请输入版本号（例如 1.0.0）:

echo.
echo ▶️  设置版本号
call scripts\set-version.bat %VERSION%
if errorlevel 1 (
    echo ❌ 版本号设置失败。
    pause
    exit /b 1
)

echo.
echo ▶️  第一步：构建 Maven 项目
call scripts\build-maven.bat
if errorlevel 1 (
    echo ❌ Maven 构建失败，中止后续操作。
    pause
    exit /b 1
)

echo.
echo ▶️  第二步：使用 Launch4j 生成可执行文件
call scripts\build-exe.bat
if errorlevel 1 (
    echo ❌ 可执行文件打包失败，中止后续操作。
    pause
    exit /b 1
)

echo.
echo ▶️  删除 MF 文件中的版本号
call scripts\set-version.bat --revert
if errorlevel 1 (
    echo ❌ 版本号删除失败。
    pause
    exit /b 1
)

echo.
echo.
echo ▶️  第三步：获取 NAPS2 软件包
call scripts\get-naps2.bat
if errorlevel 1 (
    echo ❌ NAPS2 下载失败。
    pause
    exit /b 1
)

echo.
echo ▶️  第四步：获取 JRE 软件包
call scripts\get-jre.bat
if errorlevel 1 (
    echo ❌ JRE 下载失败。
    pause
    exit /b 1
)

echo.
echo ▶️  第四步：获取驱动软件包
call scripts\get-driver.bat
if errorlevel 1 (
    echo ❌ 驱动软件包下载失败。
    pause
    exit /b 1
)

echo.
echo ▶️  第五步：使用 Inno Setup 编译安装程序
call scripts\build-installer.bat %VERSION%
if errorlevel 1 (
    echo ❌ 安装包编译失败。
    pause
    exit /b 1
)

echo.
echo ✅ 所有步骤执行完毕！
pause
