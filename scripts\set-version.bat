@echo off
chcp 65001 > nul
cd /d %~dp0..

set MANIFEST_FILE=%cd%\src\main\java\META-INF\MANIFEST.MF

if not exist "%MANIFEST_FILE%" (
    echo ❌ 找不到文件 %MANIFEST_FILE%
    pause
    exit /b 1
)

if "%~1"=="--revert" (
    echo 🔁 正在撤销 Implementation-Version 行...

    powershell -Command ^
        "$file = '%MANIFEST_FILE%';" ^
        "$content = Get-Content $file | Where-Object { $_ -notmatch '^Implementation-Version:' };" ^
        "[System.IO.File]::WriteAllLines($file, $content, (New-Object System.Text.UTF8Encoding($false)))"

    if %errorlevel% neq 0 (
        echo ❌ 撤销失败
        pause
        exit /b 1
    ) else (
        echo ✅ 已成功移除 Implementation-Version 行
        exit /b 0
    )
)

rem 否则视为设置版本号
if "%~1"=="" (
    set /p VERSION=请输入版本号（例如 1.0.0）: 
) else (
    set VERSION=%~1
)

echo 版本号为：%VERSION%

powershell -Command ^
    "$file = '%MANIFEST_FILE%';" ^
    "$version = '%VERSION%';" ^
    "$content = Get-Content $file;" ^
    "$found = $false;" ^
    "$content = $content | ForEach-Object { " ^
    "  if ($_ -match '^Implementation-Version:') { " ^
    "    $found = $true; " ^
    "    'Implementation-Version: ' + $version " ^
    "  } else { $_ }" ^
    "};" ^
    "if (-not $found) { $content += 'Implementation-Version: ' + $version };" ^
    "[System.IO.File]::WriteAllLines($file, $content, (New-Object System.Text.UTF8Encoding($false)))"

if %errorlevel% neq 0 (
    echo ❌ 修改失败
    pause
    exit /b 1
) else (
    echo ✅ 成功更新版本号
)
