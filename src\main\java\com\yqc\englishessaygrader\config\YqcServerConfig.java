package com.yqc.englishessaygrader.config;

public class YqcServerConfig {

    private static final YqcServerConfig INSTANCE = new YqcServerConfig();
    private String uploadServerUrl;
    private String naps2ConsolePath;

    private YqcServerConfig() {
        loadConfigFromEnvironment();
    }

    public static YqcServerConfig getInstance() {
        return INSTANCE;
    }

    private void loadConfigFromEnvironment() {
        // 从环境变量中读取配置，如果未设置则使用默认值
        this.uploadServerUrl = System.getenv("YQC_EEG_API_URL");
        if (this.uploadServerUrl == null || this.uploadServerUrl.trim().isEmpty()) {
            this.uploadServerUrl = "https://yiqichuangrobot.com/robotProd-api";
        }

        this.naps2ConsolePath = System.getenv("NAPS2_CONSOLE_PATH");
        if (this.naps2ConsolePath == null || this.naps2ConsolePath.trim().isEmpty()) {
            this.naps2ConsolePath = "naps2/App/NAPS2.Console.exe";
        }

        // 打印配置信息到日志
        printConfigurationInfo();
    }

    public String getUploadServerUrl() {
        return uploadServerUrl;
    }

    public String getNaps2ConsolePath() {
        return naps2ConsolePath;
    }

    /**
     * 打印配置信息到日志
     */
    private void printConfigurationInfo() {
        try {
            System.out.println("=== YQC Server Configuration ===");

            // 打印服务器URL配置
            String envServerUrl = System.getenv("YQC_EEG_API_URL");
            String serverUrlSource = envServerUrl != null ? "环境变量 YQC_EEG_API_URL" : "默认值";
            System.out.println("服务器URL: " + this.uploadServerUrl + " (来源: " + serverUrlSource + ")");

            // 打印NAPS2控制台路径配置
            String envNaps2Path = System.getenv("NAPS2_CONSOLE_PATH");
            String naps2PathSource = envNaps2Path != null ? "环境变量 NAPS2_CONSOLE_PATH" : "默认值";
            System.out.println("NAPS2 Console路径: " + this.naps2ConsolePath + " (来源: " + naps2PathSource + ")");

            System.out.println("=== YQC Server Configuration End ===");
            System.out.println();

        } catch (Exception e) {
            System.err.println("打印配置信息时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

}
