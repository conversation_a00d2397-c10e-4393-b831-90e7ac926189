package com.yqc.englishessaygrader.scanner;

import com.yqc.englishessaygrader.config.YqcServerConfig;
import com.yqc.englishessaygrader.model.ScanConfig;
import com.yqc.englishessaygrader.model.ScanResult;
import okhttp3.*;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 扫描仪控制器类
 * 负责与NAPS2交互并执行扫描操作
 */
public class ScannerController {
    private static final String NAPS2_CONSOLE_PATH = YqcServerConfig.getInstance().getNaps2ConsolePath();
    private File tempDir;
    private List<String> availableScanners;
    private OkHttpClient okHttpClient;

    public ScannerController() {
        // 创建临时目录用于存储扫描图像
        try {
            tempDir = Files.createTempDirectory("scanner_temp").toFile();
            tempDir.deleteOnExit();
        } catch (IOException e) {
            e.printStackTrace();
            tempDir = new File(System.getProperty("java.io.tmpdir"), "scanner_temp");
            tempDir.mkdirs();
        }

        // 初始化可用扫描仪列表
        availableScanners = new ArrayList<>();
        this.okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(120, TimeUnit.SECONDS)
                .readTimeout(120, TimeUnit.SECONDS)
                .writeTimeout(120, TimeUnit.SECONDS)
                .build();
        refreshScannerList();
    }

    /**
     * 刷新可用扫描仪列表
     */
    public void refreshScannerList() {
        availableScanners.clear();
        try {
            // 构建命令行参数
            ProcessBuilder pb = new ProcessBuilder(
                NAPS2_CONSOLE_PATH,
                "--listdevices",
                "--driver", "wia"
            );

            pb.redirectErrorStream(true); // 合并错误流和标准输出流

            // 执行命令并获取输出
            Process process = pb.start();

            // 设置超时时间（30秒）
            boolean completed = process.waitFor(30, java.util.concurrent.TimeUnit.SECONDS);

            if (!completed) {
                // 超时处理
                process.destroyForcibly();
                System.out.println("【扫描仪列表】获取扫描仪列表超时");
                return;
            }

            // 读取输出
            String output = new String(process.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
            // 过滤掉可能的乱码字符
            output = filterNonPrintableChars(output);

            // 解析输出获取扫描仪列表
            String[] lines = output.split("\n");
            for (String line : lines) {
                if (line.trim().length() > 0) {
                    availableScanners.add(line.trim());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("【扫描仪列表】获取扫描仪列表失败: " + e.getMessage());
        }

        System.out.println("【扫描仪列表】发现" + availableScanners.size() + "个扫描仪设备");
    }

    /**
     * 获取可用扫描仪列表
     */
    public List<String> getAvailableScanners() {
        return new ArrayList<>(availableScanners);
    }

    /**
     * 执行扫描操作
     * @param config 扫描配置
     * @return 扫描结果，包含扫描图像文件路径
     */
    public ScanResult scan(ScanConfig config) {
        return scan(config, null);
    }

    /**
     * 执行扫描操作（支持取消）
     * @param config 扫描配置
     * @param cancelCallback 取消回调，当返回true时停止扫描
     * @return 扫描结果，包含扫描图像文件路径
     */
    public ScanResult scan(ScanConfig config, java.util.function.Supplier<Boolean> cancelCallback) {
        ScanResult result = new ScanResult();
        List<File> scannedFiles = new ArrayList<>();

        try {
            // 生成唯一的输出文件名
            SimpleDateFormat sdf = new SimpleDateFormat("HHmmss");
            String timestamp = sdf.format(new Date());
            String outputPattern = new File(tempDir, "scan_" + timestamp + "_$(nnnn).jpg").getAbsolutePath();

            // 构建命令行参数
            List<String> command = new ArrayList<>();
            command.add(NAPS2_CONSOLE_PATH);
            command.add("-o");
            command.add(outputPattern);
            command.add("--device");
            command.add(config.getScannerName());
            command.add("--driver");
            command.add("wia");

            // 设置双面扫描
            if (config.isDoubleSided()) {
                command.add("--source");
                command.add("duplex");
            } else {
                command.add("--source");
                command.add("feeder");
            }

            // 设置分辨率
            command.add("--dpi");
            command.add(String.valueOf(config.getResolution()));

            // 设置颜色模式
            command.add("--bitdepth");
            command.add(config.getColorMode());

            // 不使用配置文件
            command.add("--noprofile");

            // 扫描次数
//            command.add("-n");
//            command.add("1");

            // 执行扫描命令
            ProcessBuilder pb = new ProcessBuilder(command);
            pb.redirectErrorStream(true); // 合并错误流和标准输出流
            Process process = pb.start();

            // 创建一个简单的线程读取进程输出，避免缓冲区满导致阻塞
            Thread outputReader = new Thread(() -> {
                try {
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = process.getInputStream().read(buffer)) != -1) {
                        // 使用UTF-8编码读取输出
                        String output = new String(buffer, 0, bytesRead, Charset.forName("GBK"));
                        System.out.println("扫描输出-----"+output);
                        if (output.contains("输稿器中未发现纸张.")) {
                            System.out.println("停止扫描");
                            process.destroyForcibly(); // 强制终止扫描进程
                            // 设置无纸状态标志
                            result.setNoMorePaper(true);
                            return;
                        }
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            });
            outputReader.setDaemon(true);
            outputReader.start();

            // 等待扫描完成，支持用户取消
            while (process.isAlive()) {
                // 检查是否需要取消
                if (cancelCallback != null && cancelCallback.get()) {
                    process.destroyForcibly();
                    result.setSuccess(false);
                    result.setErrorMessage("用户取消了扫描操作");
                    return result;
                }

                try {
                    // 短暂等待，避免占用过多CPU
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    process.destroyForcibly();
                    result.setSuccess(false);
                    result.setErrorMessage("扫描操作被中断");
                    return result;
                }
            }

            int exitCode = process.exitValue();
            System.out.println(tempDir);
            // 查找生成的文件
            File[] files = tempDir.listFiles((dir, name) ->
                name.startsWith("scan_" + timestamp));


            if(config.isDoubleSided()){
                // 按照 scan_时间戳+0001_a, scan_时间戳+0001_b, scan_时间戳+0002_a, scan_时间戳+0002_b 的格式重命名文件
                for (int i = 0; i < files.length; i++) {
                    int group = (i / 2) + 1; // 每两个文件为一组，从1开始计数
                    String suffix = (i % 2 == 0) ? "a" : "b"; // 第一个为 a，第二个为 b
                    String newName = String.format("scan_%s_%04d_%s", timestamp, group, suffix); // 包含时间戳的新文件名
                    File newFile = new File(files[i].getParent(), newName + ".jpg");

                    if (files[i].renameTo(newFile)) {
                        System.out.println("文件名称：" + newFile.getName());
                        // 更新 scannedFiles 列表中的文件引用（如果需要）
                        scannedFiles.add(newFile);
                    } else {
                        System.err.println("重命名失败: " + files[i].getName());
                    }
                }
                // 设置扫描结果
                result.setSuccess(true);
                result.setScannedFiles(scannedFiles);
                System.out.println("【双面扫描完成】共扫描" + scannedFiles.size() + "页");

            }else{
                // 处理扫描结果
                if (exitCode == 0 && files != null && files.length > 0) {
                    // 有扫描到文件
                    for (File file : files) {
                        scannedFiles.add(file);
                    }

                    // 设置扫描结果
                    result.setSuccess(true);
                    result.setScannedFiles(scannedFiles);
                    System.out.println("【单面扫描完成】共扫描" + scannedFiles.size() + "页");
                }
//            else if (files == null || files.length == 0) {
//
//                for (File file : files) {
//                    scannedFiles.add(file);
//                }
//                System.out.println("123【扫描完成】共扫描" + scannedFiles.size() + "页");
//                result.setSuccess(true);
//                result.setScannedFiles(scannedFiles);
//
//            }
                else {
                    // 其他扫描失败情况
                    result.setSuccess(false);
                    result.setErrorMessage("扫描失败，错误代码: " + exitCode);
                    System.out.println("【扫描错误】扫描失败，错误代码: " + exitCode);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(false);
            result.setErrorMessage("扫描过程发生错误: " + e.getMessage());
            System.out.println("【扫描异常】扫描过程发生错误: " + e.getMessage());
        }

        return result;
    }


    /**
     * 将扫描图像上传到服务器
     * @param imageFiles 要上传的图像文件数组
     * @param taskId 关联的任务ID
     * @param accessToken 访问令牌
     * @return 上传是否成功
     */
    public boolean uploadToServer(List<File> imageFiles, int taskId, String accessToken) {
        if (imageFiles == null || imageFiles.size() == 0) {
            System.out.println("【上传图像】没有文件需要上传");
            return false;
        }

        // 检查所有文件是否存在
        for (File file : imageFiles) {
            if (file == null || !file.exists()) {
                System.out.println("【上传图像】存在无效文件: " + (file != null ? file.getAbsolutePath() : "null"));
                return false;
            }
        }

        try {
            // 生成分隔符
            String boundary = "----" + System.currentTimeMillis();

            // 构建 multipart/form-data 请求体
            MediaType mediaType = MediaType.parse("multipart/form-data; boundary=" + boundary);
            MultipartBody.Builder bodyBuilder = new MultipartBody.Builder()
                    .setType(mediaType)
                    .addFormDataPart("taskId", String.valueOf(taskId));

            // 添加多个文件
            for (File file : imageFiles) {
                bodyBuilder.addFormDataPart(
                        "files", // 表单字段名（如果服务端支持多个文件可使用相同 key）
                        file.getName(),
                        RequestBody.create(file, MediaType.parse("image/jpeg"))
                );
            }

            RequestBody requestBody = bodyBuilder.build();

            // 创建请求
            Request request = new Request.Builder()
                    .url(YqcServerConfig.getInstance().getUploadServerUrl()+"/paper/paperTask/upload")
                    .post(requestBody)
                    .addHeader("Authorization", "Bearer " + accessToken)
                    .build();

            // 发送请求并获取响应
            try (Response response = okHttpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    // 读取响应内容
                    String responseBody = response.body().string();
                    System.out.println("【上传图像】响应内容: " + responseBody);
                    return true;
                } else {
                    System.out.println("【上传图像】上传失败，错误响应: " + response.code());
                    return false;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("【上传图像】上传过程发生错误: " + e.getMessage());
            return false;
        }
    }


    /**
     * 过滤非打印字符
     * @param input 输入字符串
     * @return 过滤后的字符串
     */
    private String filterNonPrintableChars(String input) {
        if (input == null) {
            return "";
        }
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            // 保留ASCII可打印字符、换行和制表符
            if ((c >= 32 && c <= 126) || c == '\n' || c == '\r' || c == '\t') {
                result.append(c);
            }
        }
        return result.toString();
    }
}