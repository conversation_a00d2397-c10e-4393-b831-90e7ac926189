package com.yqc.englishessaygrader.config;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.Properties;

/**
 * 用户偏好设置管理类
 * 负责保存和加载用户的登录凭据等配置信息
 */
public class UserPreferences {
    private static final String CONFIG_DIR = getCurrentDirectory();
    private static final String CONFIG_FILE = "user.properties";
    private static final String KEY_FILE = "app.key";
    private static final String ALGORITHM = "AES";
    
    private static UserPreferences instance;
    private Properties properties;
    private SecretKey secretKey;
    
    private UserPreferences() {
        initializeConfigDirectory();
        loadOrCreateKey();
        loadProperties();
    }

    /**
     * 获取当前程序运行目录
     */
    private static String getCurrentDirectory() {
        try {
            // 获取当前程序的运行目录
            String currentDir = System.getProperty("user.dir");
            return currentDir;
        } catch (Exception e) {
            // 如果获取失败，使用当前工作目录
            return ".";
        }
    }
    
    public static synchronized UserPreferences getInstance() {
        if (instance == null) {
            instance = new UserPreferences();
        }
        return instance;
    }
    
    /**
     * 初始化配置目录
     */
    private void initializeConfigDirectory() {
        try {
            Path configPath = Paths.get(CONFIG_DIR);
            if (!Files.exists(configPath)) {
                Files.createDirectories(configPath);
            }
        } catch (Exception e) {
            System.err.println("无法访问配置目录: " + e.getMessage());
        }
    }
    
    /**
     * 加载或创建加密密钥
     */
    private void loadOrCreateKey() {
        File keyFile = new File(CONFIG_DIR, KEY_FILE);
        
        try {
            if (keyFile.exists()) {
                // 加载现有密钥
                byte[] keyBytes = Files.readAllBytes(keyFile.toPath());
                secretKey = new SecretKeySpec(keyBytes, ALGORITHM);
            } else {
                // 创建新密钥
                KeyGenerator keyGen = KeyGenerator.getInstance(ALGORITHM);
                keyGen.init(128);
                secretKey = keyGen.generateKey();
                
                // 保存密钥到文件
                Files.write(keyFile.toPath(), secretKey.getEncoded());
            }
        } catch (Exception e) {
            System.err.println("密钥处理失败: " + e.getMessage());
            // 如果密钥处理失败，创建一个默认密钥
            try {
                KeyGenerator keyGen = KeyGenerator.getInstance(ALGORITHM);
                keyGen.init(128);
                secretKey = keyGen.generateKey();
            } catch (Exception ex) {
                throw new RuntimeException("无法创建加密密钥", ex);
            }
        }
    }
    
    /**
     * 加载配置文件
     */
    private void loadProperties() {
        properties = new Properties();
        File configFile = new File(CONFIG_DIR, CONFIG_FILE);
        
        if (configFile.exists()) {
            try (FileInputStream fis = new FileInputStream(configFile)) {
                properties.load(fis);
            } catch (Exception e) {
                System.err.println("加载配置文件失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 保存配置文件
     */
    private void saveProperties() {
        File configFile = new File(CONFIG_DIR, CONFIG_FILE);
        
        try (FileOutputStream fos = new FileOutputStream(configFile)) {
            properties.store(fos, "YQC English Grader User Preferences");
        } catch (Exception e) {
            System.err.println("保存配置文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 加密字符串
     */
    private String encrypt(String plainText) {
        try {
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes());
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            System.err.println("加密失败: " + e.getMessage());
            return plainText; // 如果加密失败，返回原文
        }
    }
    
    /**
     * 解密字符串
     */
    private String decrypt(String encryptedText) {
        try {
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedText));
            return new String(decryptedBytes);
        } catch (Exception e) {
            System.err.println("解密失败: " + e.getMessage());
            return ""; // 如果解密失败，返回空字符串
        }
    }
    
    /**
     * 保存用户名
     */
    public void saveUsername(String username) {
        if (username != null && !username.trim().isEmpty()) {
            properties.setProperty("username", username);
            saveProperties();
        }
    }
    
    /**
     * 获取保存的用户名
     */
    public String getSavedUsername() {
        return properties.getProperty("username", "");
    }
    
    /**
     * 保存密码（加密存储）
     */
    public void savePassword(String password) {
        if (password != null && !password.trim().isEmpty()) {
            String encryptedPassword = encrypt(password);
            properties.setProperty("password", encryptedPassword);
            saveProperties();
        }
    }
    
    /**
     * 获取保存的密码（解密后返回）
     */
    public String getSavedPassword() {
        String encryptedPassword = properties.getProperty("password", "");
        if (encryptedPassword.isEmpty()) {
            return "";
        }
        return decrypt(encryptedPassword);
    }
    
    /**
     * 检查是否启用了记住密码功能
     */
    public boolean isRememberPasswordEnabled() {
        return Boolean.parseBoolean(properties.getProperty("rememberPassword", "false"));
    }
    
    /**
     * 设置是否记住密码
     */
    public void setRememberPassword(boolean remember) {
        properties.setProperty("rememberPassword", String.valueOf(remember));
        if (!remember) {
            // 如果不记住密码，清除保存的密码
            properties.remove("password");
        }
        saveProperties();
    }
    
    /**
     * 清除所有保存的登录信息
     */
    public void clearLoginInfo() {
        properties.remove("username");
        properties.remove("password");
        properties.remove("rememberPassword");
        saveProperties();
    }
    
    /**
     * 保存完整的登录信息
     */
    public void saveLoginInfo(String username, String password, boolean rememberPassword) {
        saveUsername(username);
        setRememberPassword(rememberPassword);
        if (rememberPassword) {
            savePassword(password);
        } else {
            properties.remove("password");
            saveProperties();
        }
    }
}
