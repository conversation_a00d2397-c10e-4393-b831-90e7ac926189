package com.yqc.englishessaygrader.ui;

import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;

import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.UIManager;

import org.json.JSONObject;

public class TaskDetailsDialog extends JDialog {
    public TaskDetailsDialog(JFrame owner, JSONObject taskInfo) {
        super(owner, "任务详情", true);
        setSize(800, 650);
        setLocationRelativeTo(owner);
        setLayout(new BorderLayout(10, 10));

        // 创建主面板
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(15, 15, 15, 15));

        JPanel contentPanel = createContentPanel(taskInfo.optString("taskContent", "无数据"));
        mainPanel.add(contentPanel, BorderLayout.CENTER);

        JPanel infoPanel = createInfoPanel(taskInfo);
        mainPanel.add(infoPanel, BorderLayout.SOUTH);

        JScrollPane scrollPane = new JScrollPane(mainPanel);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);
        add(scrollPane, BorderLayout.CENTER);

        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        JButton closeButton = new JButton("关闭");
        closeButton.setPreferredSize(new Dimension(80, 30));
        closeButton.addActionListener(e -> dispose());
        buttonPanel.add(closeButton);
        add(buttonPanel, BorderLayout.SOUTH);
    }

    /**
     * 创建题目要求面板
     */
    private JPanel createContentPanel(String taskContent) {
        JPanel panel = new JPanel(new BorderLayout(5, 5));
        panel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createEtchedBorder(), "题目要求",
            0, 0, new Font("Dialog", Font.BOLD, 14)));

        JTextArea contentArea = new JTextArea(taskContent);
        contentArea.setWrapStyleWord(true);
        contentArea.setLineWrap(true);
        contentArea.setEditable(false);
        contentArea.setBackground(UIManager.getColor("Panel.background"));
        contentArea.setFont(new Font("Dialog", Font.PLAIN, 13));
        contentArea.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        JScrollPane scrollPane = new JScrollPane(contentArea);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);
        scrollPane.setPreferredSize(new Dimension(750, 200));

        panel.add(scrollPane, BorderLayout.CENTER);
        return panel;
    }

    /**
     * 创建其他信息面板
     */
    private JPanel createInfoPanel(JSONObject taskInfo) {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createEtchedBorder(), "任务信息",
            0, 0, new Font("Dialog", Font.BOLD, 14)));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(8, 10, 8, 10);
        gbc.anchor = GridBagConstraints.WEST;

        // 第一行：任务班级和任务等级
        addCompactField(panel, gbc, 0, 0, "任务班级", taskInfo.optString("taskClass", "无数据"));
        addCompactField(panel, gbc, 2, 0, "任务学段", taskInfo.optString("taskGrade", "无数据"));

        // 第二行：任务分数和任务难度
        addCompactField(panel, gbc, 0, 1, "任务分数", taskInfo.optString("taskNumber", "无数据"));
        addCompactField(panel, gbc, 2, 1, "任务难度", taskInfo.optString("taskDifficulty", "无数据"));

        // 第三行：词数限制和识别类型
        String wordLimit = taskInfo.optString("taskWordNumMin", "无数据") + " - " +
                          taskInfo.optString("taskWordNumMax", "无数据");
        addCompactField(panel, gbc, 0, 2, "词数限制", wordLimit);

        // 将服务器返回的识别类型转换为用户友好的显示文本
        String scanType = taskInfo.optString("taskScanType", "无数据");
        addCompactField(panel, gbc, 2, 2, "识别类型", scanType);

        // 第四行：备注信息（跨两列）
        addWideField(panel, gbc, 0, 3, "备注信息", taskInfo.optString("remake", "无数据"));

        return panel;
    }

    /**
     * 添加紧凑的单行字段
     */
    private void addCompactField(JPanel panel, GridBagConstraints gbc, int x, int y, String label, String value) {
        gbc.gridx = x;
        gbc.gridy = y;
        gbc.gridwidth = 1;
        gbc.fill = GridBagConstraints.NONE;

        JLabel keyLabel = new JLabel(label + "：");
        keyLabel.setFont(new Font("Dialog", Font.BOLD, 12));
        keyLabel.setPreferredSize(new Dimension(80, 25));
        panel.add(keyLabel, gbc);

        gbc.gridx = x + 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;

        JTextField valueField = new JTextField(value);
        valueField.setEditable(false);
        valueField.setBackground(UIManager.getColor("Panel.background"));
        valueField.setBorder(BorderFactory.createEmptyBorder(2, 5, 2, 5));
        valueField.setPreferredSize(new Dimension(200, 25));
        panel.add(valueField, gbc);

        gbc.weightx = 0.0; // 重置权重
    }

    /**
     * 添加跨列的字段（用于备注等较长内容）
     */
    private void addWideField(JPanel panel, GridBagConstraints gbc, int x, int y, String label, String value) {
        gbc.gridx = x;
        gbc.gridy = y;
        gbc.gridwidth = 1;
        gbc.fill = GridBagConstraints.NONE;

        JLabel keyLabel = new JLabel(label + "：");
        keyLabel.setFont(new Font("Dialog", Font.BOLD, 12));
        keyLabel.setPreferredSize(new Dimension(80, 25));
        panel.add(keyLabel, gbc);

        gbc.gridx = x + 1;
        gbc.gridwidth = 3; // 跨三列
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;

        JTextField valueField = new JTextField(value);
        valueField.setEditable(false);
        valueField.setBackground(UIManager.getColor("Panel.background"));
        valueField.setBorder(BorderFactory.createEmptyBorder(2, 5, 2, 5));
        valueField.setPreferredSize(new Dimension(400, 25));
        panel.add(valueField, gbc);

        gbc.weightx = 0.0; // 重置权重
        gbc.gridwidth = 1; // 重置列宽
    }
}

