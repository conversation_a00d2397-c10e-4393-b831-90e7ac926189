package com.yqc.englishessaygrader;

import java.io.FileOutputStream;
import java.io.PrintStream;

import javax.swing.SwingUtilities;
import javax.swing.UIManager;

import com.yqc.englishessaygrader.scanner.ScannerController;
import com.yqc.englishessaygrader.service.TaskService;
import com.yqc.englishessaygrader.ui.LoginFrame;

/**
 * 扫描仪应用程序主类
 * 基于NAPS2功能实现的Java Swing扫描仪应用
 */
public class PaperScannerApp {
    
    public static void main(String[] args) {

        // 重定向 System.out 和 System.err 到日志文件
        try {
            PrintStream logStream = new PrintStream(new FileOutputStream("app.log", false), true);
            System.setOut(logStream);
            System.setErr(logStream);
            System.out.println("=== Application started ===");
        } catch (Exception e) {
            e.printStackTrace(); // 如果日志初始化失败，则打印到控制台
        }

        try {
            // 设置本地系统外观
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        // 使用Swing事件分发线程创建和显示GUI
        SwingUtilities.invokeLater(() -> {
            // 创建扫描控制器
            ScannerController scannerController = new ScannerController();
            TaskService taskService = new TaskService();
            
            // 创建并显示登录窗口
            LoginFrame loginFrame = new LoginFrame(scannerController,taskService);
            loginFrame.setVisible(true);
        });
    }
}