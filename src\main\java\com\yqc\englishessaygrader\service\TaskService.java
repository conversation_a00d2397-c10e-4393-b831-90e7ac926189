package com.yqc.englishessaygrader.service;

import com.yqc.englishessaygrader.config.YqcServerConfig;
import com.yqc.englishessaygrader.model.Task;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 任务服务类
 * 用于处理与服务器的任务相关通信
 */
public class TaskService {
    private static final OkHttpClient okHttpClient = new OkHttpClient();

    /**
     * 获取任务列表
     * @param accessToken 访问令牌
     * @return 任务列表
     * @throws Exception 如果请求失败
     */
    public static List<Task> getTaskList(String accessToken) throws Exception {
        List<Task> tasks = new ArrayList<>();

        System.out.println("开始获取任务列表，accessToken: " + (accessToken != null ? accessToken.substring(0, Math.min(10, accessToken.length())) + "..." : "null"));

        // 创建请求
        Request request = new Request.Builder()
            .url(YqcServerConfig.getInstance().getUploadServerUrl()+"/paper/paperTask/list")
            .get()
            .addHeader("Authorization", "Bearer " + accessToken)
            .build();

        System.out.println("发送请求到: " + request.url());

        // 发送请求并获取响应
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                // 读取响应内容
                String responseStr = response.body().string();
                System.out.println("服务器响应内容: " + responseStr);

                // 解析JSON响应
                JSONObject jsonResponse = new JSONObject(responseStr);
                if (jsonResponse.has("rows") && !jsonResponse.isNull("rows")) {
                    JSONArray dataArray = jsonResponse.getJSONArray("rows");
                    System.out.println("获取到任务数量: " + dataArray.length());

                    for (int i = 0; i < dataArray.length(); i++) {
                        JSONObject taskJson = dataArray.getJSONObject(i);
                        Task task = new Task();
                        // 设置任务属性
                        if (taskJson.has("taskId")) task.setTaskId(taskJson.getInt("taskId"));
                        if (taskJson.has("taskContent")) task.setTaskContent(taskJson.getString("taskContent"));
                        if (taskJson.has("taskType")) task.setTaskType(taskJson.getString("taskType"));
                        if (taskJson.has("taskGrade")) task.setTaskGrade(taskJson.getString("taskGrade"));
                        if (taskJson.has("taskNumber")) task.setTaskNumber(taskJson.getString("taskNumber"));
                        if (taskJson.has("taskDifficulty")) task.setTaskDifficulty(taskJson.getString("taskDifficulty"));
                        if (taskJson.has("taskWordNumMin")) task.setTaskWordNumMin(taskJson.getString("taskWordNumMin"));
                        if (taskJson.has("taskWordNumMax")) task.setTaskWordNumMax(taskJson.getString("taskWordNumMax"));
                        if (taskJson.has("taskClass")) task.setTaskClass(taskJson.optString("taskClass"));
                        if (taskJson.has("remake")) task.setRemake(taskJson.optString("remake", ""));
                        if (taskJson.has("createTime")) task.setCreateTime(taskJson.getString("createTime"));

                        tasks.add(task);
                    }
                } else {
                    System.out.println("响应中没有data字段或data为null");
                }
            } else {
                // 请求失败
                String errorResponse = response.body() != null ? response.body().string() : "未知错误";
                System.out.println("获取任务列表失败，错误响应: " + errorResponse);
                throw new Exception("获取任务列表失败: " + errorResponse);
            }
        } catch (IOException e) {
            e.printStackTrace();
            System.out.println("获取任务列表失败，IO错误: " + e.getMessage());
            throw new Exception("获取任务列表失败: " + e.getMessage());
        }

        return tasks;
    }

    /**
     * 根据任务ID获取任务详情
     *
     * @param taskId      任务ID
     * @param accessToken 访问令牌
     * @return 返回对应的任务对象
     * @throws Exception 如果请求失败
     */
    public static JSONObject getTaskById(int taskId, String accessToken) throws Exception {
        // 创建请求URL
        String url = YqcServerConfig.getInstance().getUploadServerUrl()+"/paper/paperTask/" + taskId;

        // 构建请求
        Request request = new Request.Builder()
            .url(url)
            .get()
            .addHeader("Authorization", "Bearer " + accessToken)
            .build();

        System.out.println("发送请求获取任务详情: " + url);

        // 发送请求并处理响应
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                System.out.println("获取任务详情响应内容: " + responseBody);

                JSONObject jsonResponse = new JSONObject(responseBody);
                if (!jsonResponse.isNull("data")) {
                    JSONObject taskJson = jsonResponse.getJSONObject("data");
                    return taskJson;
                } else {
                    System.out.println("响应中data字段为null");
                    throw new Exception("无法获取任务详情，data字段为空");
                }
            } else {
                String errorResponse = response.body() != null ? response.body().string() : "未知错误";
                System.out.println("获取任务详情失败，错误响应: " + errorResponse);
                throw new Exception("获取任务详情失败: " + errorResponse);
            }
        } catch (IOException e) {
            e.printStackTrace();
            System.out.println("获取任务详情失败，IO异常: " + e.getMessage());
            throw new Exception("获取任务详情失败: " + e.getMessage());
        }
    }

    /**
     * 删除指定ID的任务
     *
     * @param taskId      任务ID
     * @param accessToken 访问令牌
     * @return 返回服务器响应的JSON对象
     * @throws Exception 如果请求失败
     */
    public static JSONObject deleteTask(int taskId, String accessToken) throws Exception {
        // 创建请求URL
        String url = YqcServerConfig.getInstance().getUploadServerUrl()+"/paper/paperTask/delete/" + taskId;

        // 构建请求
        Request request = new Request.Builder()
                .url(url)
                .delete()
                .addHeader("Authorization", "Bearer " + accessToken)
                .build();

        System.out.println("发送请求删除任务: " + url);

        // 发送请求并处理响应
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                System.out.println("删除任务响应内容: " + responseBody);

                JSONObject jsonResponse = new JSONObject(responseBody);
                return jsonResponse;
            } else {
                String errorResponse = response.body() != null ? response.body().string() : "未知错误";
                System.out.println("删除任务失败，错误响应: " + errorResponse);
                throw new Exception("删除任务失败: " + errorResponse);
            }
        } catch (IOException e) {
            e.printStackTrace();
            System.out.println("删除任务失败，IO异常: " + e.getMessage());
            throw new Exception("删除任务失败: " + e.getMessage());
        }
    }

}
