
# 英语批改助手

## 驱动、补丁及注册列表处理说明

打包后的程序安装包在安装完成后将自动执行 `setup.bat` 脚本，对操作系统进行以下配置，以确保程序功能的正常运行。

### 1. 安装扫描仪驱动程序

脚本会自动安装扫描仪主驱动程序 `PSIPTWAIN-1_60_0.exe`，该驱动是系统识别并调用扫描设备所必需的组件。

### 2. 系统版本检测与补丁安装（仅限 Windows 7）

脚本会自动检测当前操作系统版本号。如果检测到系统为 Windows 7（主版本号为 `6.1`），将自动执行以下补丁安装步骤：

* 安装 KB2813430 补丁（`Windows6.1-KB2813430-x64.msu`）：提升图像采集兼容性；
* 安装 .NET Framework 4.6.2（`NDP462-KB3151800-x86-x64-AllOS-ENU.exe`）：确保应用程序运行所需的运行时环境。

### 3. 注册表配置：URL 协议绑定

为支持通过自定义 URL 协议调用程序（通过浏览器点击 `mokeaipcaides://` 链接启动程序），脚本将自动完成以下注册表项写入：

* 新建协议键值：`HKEY_CLASSES_ROOT\mokeaipcaides`；
* 设置描述值为：`URL:YQCEnglishEssayGrader Protocol`；
* 设置调用命令：绑定实际可执行程序路径 `YQCEnglishEssayGrader.exe`，该路径将自动从默认安装位置中搜索获取。

## 项目自动化构建说明

本项目使用 Windows 批处理脚本进行自动化构建，包含 Maven 项目编译、Launch4j 打包可执行文件及 Inno Setup 编译安装包。
为了确保脚本正常运行，请提前准备以下环境和依赖。

### 依赖软件及环境配置

1. **Java & Maven（推荐使用 mvnd 加速）**

   - 请确保已安装 [JDK](https://download.java.net/java/GA/jdk24.0.1/24a58e0e276943138bf3e963e6291ac2/9/GPL/openjdk-24.0.1_windows-x64_bin.zip)（版本 12 及以上）。
   - 安装 Maven 或 mvnd（Maven Daemon），用于项目构建。
   - 请将 `mvn` 或 `mvnd` 命令所在目录添加到系统环境变量 `PATH` 中。
   - 确保 `mvn` 或 `mvnd.cmd`（Maven 命令行）可在命令行执行。

2. **Launch4j**

   - 下载地址：[Launch4j](https://sourceforge.net/projects/launch4j/files/launch4j-3/3.50/launch4j-3.50-win32.zip/download)
   - 请将 Launch4j 的安装路径（包含 `launch4jc.exe`）添加到系统环境变量 `PATH` 中。
   - 确保 `launch4jc.exe`（Launch4j 命令行）可在命令行执行。

3. **Inno Setup Compiler v5**

   - 下载地址：[Inno Setup](https://files.jrsoftware.org/is/5/isetup-5.5.8.exe)
   - 注意需要下载 v5.x.x 的版本
   - 请将 Inno Setup 的安装路径（包含 `ISCC.exe`）添加到系统环境变量 `PATH` 中。
   - 确保 `ISCC.exe`（Inno Setup 命令行编译器）可在命令行执行。

### 环境变量配置示例

```powershell
# 示例：将 Maven 添加到 PATH（根据实际安装路径调整）
setx PATH "%PATH%;D:\jdk-24.0.1\bin"

# 示例：将 Launch4j 添加到 PATH（如果想使用默认脚本路径外的位置）
setx PATH "%PATH%;D:\Launch4j"

# 示例：将 Inno Setup 添加到 PATH
setx PATH "%PATH%;D:\Inno Setup 5"
```

设置完环境变量后，建议关闭并重新打开命令行窗口，以便变量生效。

### 使用说明

然后执行 `build.bat`，让构建变得轻松简单！祝你使用顺利~

> ps: 如果不想要每次都执行一整套的指令，可以手动运行 script 下的分立脚本。

也可以在 `build.bat` 后指定环境参数，如`build.bat prod`、`build.bat dev`、`build.bat test`，分别对应不同的构建环境。

> ps: prod 环境为生产环境，dev 环境为开发环境，test 环境为测试环境

### 注意事项

- 由于 NAPS 及 JRE 资源在 github 上，在运行 `build.bat` 脚本时要对 curl.exe 设置代理转发
- 脚本会自动检查关键工具是否存在，若找不到会提示错误并停止执行
- Inno Setup 编译安装包时，脚本会提示输入版本号，请按提示输入格式如 `1.0.0`
- 如需修改 Launch4j 或 Inno Setup 工具路径，请调整对应的批处理脚本中的变量配置

### 常见问题

- **找不到 `launch4jc.exe` 或 `ISCC.exe`**
  请确认软件安装路径正确，且环境变量配置有效

- **构建失败，请查看命令行输出日志，确认 JDK 和 Maven 配置正确**

- **脚本中优先使用 mvnd，如不存在自动切换为 mvn**

### 测试服务器连接指南

软件在开启时会读取 `YQC_EEG_API_URL` 这个环境变量，如果未设置，则使用默认值 `https://yiqichuangrobot.com/robotProd-api`
可以通过设置系统的环境变量来覆盖这个值，以连接到不同的服务器，比如本地或者是测试服务器。

同样的也可以通过指定 `NAPS2_CONSOLE_PATH` 这个环境变量，来覆盖默认的 NAPS2 控制台路径，以使用不同的 NAPS2 版本。
