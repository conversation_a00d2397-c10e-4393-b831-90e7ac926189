#define AppName "YQCEnglishEssayGrader"

[Setup]
AppName={#AppName}
AppVersion={#Version}
DefaultDirName={userappdata}\{#AppName}
OutputDir=../dist
OutputBaseFilename={#AppName}-v{#Version}
PrivilegesRequired=none
; 禁用用户修改安装路径
DisableDirPage=yes
UsePreviousAppDir=no
; 禁用“选择开始菜单文件夹”页面
DisableProgramGroupPage=yes
; 禁用“准备安装”页面
DisableReadyPage=yes

[Files]
Source: "../target/YQCEnglishEssayGrader.exe"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs;
Source: "../runtime/jre/*"; DestDir: "{app}/jre"; Flags: ignoreversion recursesubdirs createallsubdirs;
Source: "../utils/naps2/*"; DestDir: "{app}/naps2"; Flags: ignoreversion recursesubdirs createallsubdirs;
Source: "./setup.bat"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs;
Source: "../driver/*"; DestDir: "{app}/driver"; Flags: ignoreversion;
Source: "./logo.ico"; DestDir: "{app}"; Flags: ignoreversion;

[Icons]
Name: "YQC\YQCEnglishEssayGrader"; Filename: "{app}\YQCEnglishEssayGrader.exe"; IconFilename: "{app}\logo.ico"
Name: "{commondesktop}\YQCEnglishEssayGrader"; Filename: "{app}\YQCEnglishEssayGrader.exe"; IconFilename: "{app}\logo.ico"

[Run]
Filename: "{app}\setup.bat";

