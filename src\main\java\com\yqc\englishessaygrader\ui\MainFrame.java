package com.yqc.englishessaygrader.ui;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Cursor;
import java.awt.Desktop;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.GridLayout;
import java.awt.Image;
import java.awt.Insets;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.imageio.ImageIO;
import javax.swing.BorderFactory;
import javax.swing.ImageIcon;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JProgressBar;
import javax.swing.JScrollPane;
import javax.swing.SwingWorker;
import javax.swing.border.EmptyBorder;
import javax.swing.border.TitledBorder;

import org.json.JSONObject;

import com.yqc.englishessaygrader.model.ScanConfig;
import com.yqc.englishessaygrader.model.ScanResult;
import com.yqc.englishessaygrader.scanner.ScannerController;
import com.yqc.englishessaygrader.service.TaskService;
import com.yqc.englishessaygrader.utils.VersionUtil;

/**
 * 扫描仪应用程序主窗口
 */
public class MainFrame extends JFrame {
    private ScannerController scannerController;
    private ScanConfig scanConfig;
    private List<File> scannedImages;
    private int currentImageIndex;
    private String accessToken; // 保存登录成功后的access_token
    private TaskService taskService;
    
    // UI组件
    private JComboBox<String> scannerComboBox;
    private JButton scanButton;
    private JButton refreshButton;
    private JButton prevButton;
    private JButton nextButton;
    private JButton clearButton; // 清除试卷按钮
    private JLabel imageLabel;
    private JLabel statusLabel;
    private TaskListPanel taskListPanel; // 添加任务列表面板引用

    public MainFrame(ScannerController scannerController, String accessToken, TaskService taskService) {
        this.scannerController = scannerController;
        this.taskService = taskService;
        this.scanConfig = new ScanConfig();
        this.scannedImages = new ArrayList<>();
        this.currentImageIndex = -1;
        this.accessToken = accessToken;

        initUI();
    }

    private void initUI() {
        String version = VersionUtil.getVersion();
        setTitle("YQC 英语批改助手 (v" + version + ")");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1000, 800); // 增加窗口大小，提供更多空间
        setLocationRelativeTo(null);

        // 创建主面板，使用更大的间距
        JPanel mainPanel = new JPanel(new BorderLayout(15, 15));
        mainPanel.setBorder(new EmptyBorder(15, 15, 15, 15));

        // 创建配置面板
        JPanel configPanel = createConfigPanel();
        mainPanel.add(configPanel, BorderLayout.NORTH);

        // 创建中央面板（上下分布的图像管理和任务管理）
        JPanel centerPanel = new JPanel(new GridLayout(2, 1, 0, 15)); // 使用GridLayout确保上下等分

        // 创建图像预览面板（上方）
        JPanel previewPanel = createPreviewPanel();
        centerPanel.add(previewPanel);

        // 创建任务管理面板（下方）
        taskListPanel = new TaskListPanel(accessToken, scannerController);
        JPanel taskPanel = new JPanel(new BorderLayout());
        taskPanel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createEtchedBorder(), "任务管理", TitledBorder.LEFT, TitledBorder.TOP
        ));
        taskPanel.add(taskListPanel, BorderLayout.CENTER);
        centerPanel.add(taskPanel);

        // 将中央面板添加到主面板
        mainPanel.add(centerPanel, BorderLayout.CENTER);

        // 创建状态栏，使用更美观的样式
        statusLabel = new JLabel("就绪");
        statusLabel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createMatteBorder(1, 0, 0, 0, Color.LIGHT_GRAY),
            BorderFactory.createEmptyBorder(5, 10, 5, 10)
        ));
        statusLabel.setFont(new Font(statusLabel.getFont().getName(), Font.PLAIN, 12));
        mainPanel.add(statusLabel, BorderLayout.SOUTH);

        // 设置内容面板
        setContentPane(mainPanel);

        // 初始化扫描仪列表
        updateScannerList();
    }

    private JPanel createConfigPanel() {
        // 使用更美观的面板样式
        JPanel panel = new JPanel(new BorderLayout(10, 10));
        panel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createEtchedBorder(), "扫描配置", TitledBorder.LEFT, TitledBorder.TOP
        ));

        // 创建扫描配置面板
        JPanel scanConfigPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.anchor = GridBagConstraints.WEST;

        // 扫描仪选择
        gbc.gridx = 0;
        gbc.gridy = 0;
        scanConfigPanel.add(new JLabel("扫描仪:"), gbc);

        scannerComboBox = new JComboBox<>();
        scannerComboBox.setPreferredSize(new Dimension(300, 25));
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        scanConfigPanel.add(scannerComboBox, gbc);

        refreshButton = new JButton("刷新");
        refreshButton.addActionListener(e -> updateScannerList());
        gbc.gridx = 2;
        gbc.weightx = 0.0;
        scanConfigPanel.add(refreshButton, gbc);

        // 扫描按钮
        scanButton = new JButton("开始扫描");
        scanButton.addActionListener(e -> startScan());
        gbc.gridx = 0;
        gbc.gridy = 1;
        gbc.gridwidth = 3;
        scanConfigPanel.add(scanButton, gbc);

        panel.add(scanConfigPanel, BorderLayout.CENTER);

        return panel;
    }

    private JPanel createPreviewPanel() {
        JPanel panel = new JPanel(new BorderLayout(10, 10));
        panel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createEtchedBorder(), "图像管理", TitledBorder.LEFT, TitledBorder.TOP
        ));

        // 创建图像预览区域
        imageLabel = new JLabel("无图像", JLabel.CENTER);
        imageLabel.setPreferredSize(new Dimension(400, 300));
        imageLabel.setBorder(BorderFactory.createLineBorder(Color.LIGHT_GRAY));
        imageLabel.setBackground(Color.WHITE);
        imageLabel.setOpaque(true);

        JScrollPane scrollPane = new JScrollPane(imageLabel);
        scrollPane.setPreferredSize(new Dimension(400, 300));
        panel.add(scrollPane, BorderLayout.CENTER);

        // 创建图像导航按钮面板
        JPanel navPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 5));

        prevButton = new JButton("上一张");
        prevButton.addActionListener(e -> showPreviousImage());
        prevButton.setEnabled(false);
        navPanel.add(prevButton);

        nextButton = new JButton("下一张");
        nextButton.addActionListener(e -> showNextImage());
        nextButton.setEnabled(false);
        navPanel.add(nextButton);

        // 添加清除试卷按钮
        clearButton = new JButton("清除试卷");
        clearButton.addActionListener(e -> clearScannedImages());
        clearButton.setEnabled(false);
        navPanel.add(clearButton);

        panel.add(navPanel, BorderLayout.SOUTH);

        return panel;
    }

    private void updateScannerList() {
        setCursor(Cursor.getPredefinedCursor(Cursor.WAIT_CURSOR));
        statusLabel.setText("正在获取扫描仪列表...");

        // 清空当前列表
        scannerComboBox.removeAllItems();

        // 刷新扫描仪列表
        scannerController.refreshScannerList();
        List<String> scanners = scannerController.getAvailableScanners();

        // 添加到下拉框
        for (String scanner : scanners) {
            scannerComboBox.addItem(scanner);
        }

        if (scanners.isEmpty()) {
            statusLabel.setText("未找到扫描仪设备");
        } else {
            statusLabel.setText("找到 " + scanners.size() + " 个扫描仪设备");
            scannerComboBox.setSelectedIndex(0);
        }

        setCursor(Cursor.getDefaultCursor());
    }

    private void startScan() {
        // 获取扫描配置
        String selectedScanner = (String) scannerComboBox.getSelectedItem();
        if (selectedScanner == null || selectedScanner.isEmpty()) {
            JOptionPane.showMessageDialog(this,
                "请选择扫描仪", "扫描错误", JOptionPane.ERROR_MESSAGE);
            return;
        }

        // 扫描前强提示对话框（从资源目录加载图片）
        java.net.URL imgUrl = getClass().getResource("/images/scan_direction_tip.png");
        ImageIcon directionIcon = imgUrl != null ? new ImageIcon(imgUrl) : null;
        JLabel imgLabel = directionIcon != null ? new JLabel(directionIcon) : new JLabel("[未找到图片]");
        JLabel textLabel = new JLabel("请确认试卷已正确放置在扫描仪上。参考下方示意图。\n\n确认无误后点击“继续扫描”。");
        textLabel.setFont(new Font(textLabel.getFont().getName(), Font.BOLD, 16));
        textLabel.setForeground(Color.RED);
        JPanel tipPanel = new JPanel(new BorderLayout(10, 10));
        tipPanel.add(imgLabel, BorderLayout.CENTER);
        tipPanel.add(textLabel, BorderLayout.SOUTH);

        int confirm = JOptionPane.showConfirmDialog(this, tipPanel, "请确认试卷放置方向", JOptionPane.OK_CANCEL_OPTION, JOptionPane.WARNING_MESSAGE);
        if (confirm != JOptionPane.OK_OPTION) {
            return;
        }

        // 更新扫描配置
        scanConfig.setScannerName(selectedScanner);
        scanConfig.setDoubleSided(true); // 始终启用双面扫描
        scanConfig.setResolution(300); // 固定分辨率为300dpi
        scanConfig.setColorMode("color"); // 固定为彩色模式

        // 禁用扫描按钮
        scanButton.setEnabled(false);
        statusLabel.setText("正在扫描...");
        setCursor(Cursor.getPredefinedCursor(Cursor.WAIT_CURSOR));

        // 创建进度对话框
        JDialog progressDialog = new JDialog(this, "扫描进度", true);
        JProgressBar progressBar = new JProgressBar();
        progressBar.setIndeterminate(true); // 不确定进度的进度条
        progressBar.setStringPainted(true);
        progressBar.setString("正在扫描，请稍候...");

        JButton cancelButton = new JButton("取消扫描");
        JPanel progressPanel = new JPanel(new BorderLayout(10, 10));
        progressPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        progressPanel.add(new JLabel("扫描进行中，您可以随时取消操作", JLabel.CENTER), BorderLayout.NORTH);
        progressPanel.add(progressBar, BorderLayout.CENTER);
        progressPanel.add(cancelButton, BorderLayout.SOUTH);

        progressDialog.add(progressPanel);
        progressDialog.setSize(400, 150);
        progressDialog.setLocationRelativeTo(this);
        progressDialog.setDefaultCloseOperation(JDialog.DO_NOTHING_ON_CLOSE);

        // 用于控制扫描取消的标志
        final boolean[] cancelled = {false};

        // 在后台线程中执行扫描
        SwingWorker<ScanResult, Void> worker = new SwingWorker<ScanResult, Void>() {
            @Override
            protected ScanResult doInBackground() throws Exception {
                return scannerController.scan(scanConfig, () -> cancelled[0]);
            }

            @Override
            protected void done() {
                try {
                    progressDialog.dispose(); // 关闭进度对话框

                    ScanResult result = get();
                    if (result.isSuccess()) {
                        // 扫描成功
                        List<File> newFiles = result.getScannedFiles();
                        System.out.println(newFiles.size()+"---------12313123123131");
                        if (!newFiles.isEmpty()) {
                            // 添加新扫描的图像
                            scannedImages.addAll(newFiles);
                            // 更新任务列表面板中的扫描图像引用
                            taskListPanel.setScannedImages(scannedImages);
                            // 显示最新扫描的图像
                            currentImageIndex = scannedImages.size() - 1;
                            showCurrentImage();
                            statusLabel.setText("扫描完成，共 " + scannedImages.size() + " 张图像");
                        } else {
                            statusLabel.setText("扫描完成，但未获取到图像");
                        }
                    } else {
                        // 扫描失败或取消
                        if (!cancelled[0]) {
                            JOptionPane.showMessageDialog(MainFrame.this,
                                "扫描失败: " + result.getErrorMessage(),
                                "扫描错误", JOptionPane.ERROR_MESSAGE);
                        }
                        statusLabel.setText(cancelled[0] ? "扫描已取消" : "扫描失败");
                    }
                } catch (Exception e) {
                    progressDialog.dispose(); // 确保对话框被关闭
                    e.printStackTrace();
                    if (!cancelled[0]) {
                        JOptionPane.showMessageDialog(MainFrame.this,
                            "扫描过程发生错误: " + e.getMessage(),
                            "错误", JOptionPane.ERROR_MESSAGE);
                    }
                    statusLabel.setText("扫描错误");
                } finally {
                    // 恢复UI
                    scanButton.setEnabled(true);
                    setCursor(Cursor.getDefaultCursor());
                }
            }
        };

        // 取消按钮事件
        cancelButton.addActionListener(e -> {
            cancelled[0] = true;
            progressDialog.dispose();
            statusLabel.setText("正在取消扫描...");
        });

        worker.execute();

        // 显示进度对话框
        progressDialog.setVisible(true);
    }

    private void displayImage(File imageFile) {
        try {
            // 读取图像文件
            BufferedImage img = ImageIO.read(imageFile);
            if (img == null) {
                imageLabel.setIcon(null);
                imageLabel.setText("无法加载图像: " + imageFile.getName());
                return;
            }

            // 调整图像大小以适应显示区域
            int labelWidth = imageLabel.getWidth();
            int labelHeight = imageLabel.getHeight();

            if (labelWidth <= 0 || labelHeight <= 0) {
                labelWidth = 600;
                labelHeight = 400;
            }

            // 计算缩放比例
            double scale = Math.min(
                (double) labelWidth / img.getWidth(),
                (double) labelHeight / img.getHeight()
            );

            // 如果图像比显示区域小，则不放大
            if (scale > 1.0) {
                scale = 1.0;
            }

            // 计算缩放后的尺寸
            int scaledWidth = (int) (img.getWidth() * scale);
            int scaledHeight = (int) (img.getHeight() * scale);

            // 创建缩放后的图像
            Image scaledImg = img.getScaledInstance(scaledWidth, scaledHeight, Image.SCALE_SMOOTH);
            ImageIcon icon = new ImageIcon(scaledImg);

            // 显示图像
            imageLabel.setIcon(icon);
            imageLabel.setText(null);

            // 更新状态栏
            statusLabel.setText("显示图像 " + (currentImageIndex + 1) + "/" + scannedImages.size() +
                " - " + imageFile.getName());
        } catch (IOException e) {
            e.printStackTrace();
            imageLabel.setIcon(null);
            imageLabel.setText("无法加载图像: " + e.getMessage());
        }
    }

    private void showPreviousImage() {
        if (currentImageIndex > 0) {
            currentImageIndex--;
            displayImage(scannedImages.get(currentImageIndex));
            updateNavigationButtons();
        }
    }

    private void showNextImage() {
        if (currentImageIndex < scannedImages.size() - 1) {
            currentImageIndex++;
            displayImage(scannedImages.get(currentImageIndex));
            updateNavigationButtons();
        }
    }

    /**
     * 显示当前索引的图像
     */
    private void showCurrentImage() {
        if (currentImageIndex >= 0 && currentImageIndex < scannedImages.size()) {
            displayImage(scannedImages.get(currentImageIndex));
            updateNavigationButtons();
        } else {
            // 如果没有图像或索引无效，清除图像显示
            imageLabel.setIcon(null);
            imageLabel.setText("无图像");
            updateNavigationButtons();
        }
    }

    /**
     * 批量上传所有扫描的图像到指定任务
     * @param taskId 任务ID
     */
    public void uploadAllImages(int taskId) throws Exception {
        if (scannedImages == null || scannedImages.isEmpty()) {
            JOptionPane.showMessageDialog(this,
                "没有可上传的图像，请先扫描文档", "上传错误", JOptionPane.ERROR_MESSAGE);
            return;
        }
        // 创建进度对话框
        JDialog progressDialog = new JDialog(this, "批量上传中", true);
        JProgressBar progressBar = new JProgressBar(0, 1);
        progressBar.setValue(0);
        progressBar.setStringPainted(true); // 显示进度文本

        JLabel progressLabel = new JLabel("正在上传图像 0/" + scannedImages.size(), JLabel.CENTER);
        progressDialog.setLayout(new BorderLayout(10, 10));
        progressDialog.add(progressLabel, BorderLayout.NORTH);
        progressDialog.add(progressBar, BorderLayout.CENTER);
        progressDialog.setSize(350, 150);
        progressDialog.setLocationRelativeTo(this);

        // 在后台线程中执行上传
        SwingWorker<Boolean, Integer> worker = new SwingWorker<Boolean, Integer>() {
            @Override
            protected Boolean doInBackground() throws Exception {
                boolean allSuccess = true;
                int count = 0;

                boolean success = scannerController.uploadToServer(scannedImages, taskId, accessToken);
                if (!success) {
                    allSuccess = false;
                }
                count++;
                publish(count); // 发布进度更新

                // 添加短暂延迟，避免服务器过载
                Thread.sleep(500);

                return allSuccess;
            }

            @Override
            protected void process(List<Integer> chunks) {
                // 更新进度条
                int progress = chunks.get(chunks.size() - 1);
                progressBar.setValue(progress);
                progressLabel.setText("正在上传图像 " + progress + "/" + scannedImages.size());
            }

            @Override
            protected void done() {
                try {
                    boolean success = get();
                    progressDialog.dispose();

                    if (success) {
                        JOptionPane.showMessageDialog(MainFrame.this,
                            "所有图像上传成功", "上传成功", JOptionPane.INFORMATION_MESSAGE);
                        try {
                            Desktop.getDesktop().browse(new URL("https://yiqichuangrobot.com/English/pages/home/<USER>").toURI());
                        } catch (Exception ex) {
                            JOptionPane.showMessageDialog(MainFrame.this,
                                    "无法打开网页: " + ex.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
                        }
                    } else {
                        JOptionPane.showMessageDialog(MainFrame.this,
                            "部分或全部图像上传失败，请重试", "上传失败", JOptionPane.WARNING_MESSAGE);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    progressDialog.dispose();
                    JOptionPane.showMessageDialog(MainFrame.this,
                        "上传过程发生错误: " + e.getMessage(), "上传错误", JOptionPane.ERROR_MESSAGE);
                }
            }
        };

        worker.execute();

        // 显示进度对话框
        progressDialog.setVisible(true);
    }

    private void updateNavigationButtons() {
        prevButton.setEnabled(currentImageIndex > 0);
        nextButton.setEnabled(currentImageIndex < scannedImages.size() - 1);
        clearButton.setEnabled(!scannedImages.isEmpty()); // 更新清除试卷按钮状态
    }

    /**
     * 清空扫描的图像列表并删除本地文件
     */
    private void clearScannedImages() {
        // 删除本地文件
        for (File file : scannedImages) {
            if (file.exists()) {
                boolean deleted = file.delete();
                if (!deleted) {
                    System.err.println("无法删除文件: " + file.getAbsolutePath());
                }
            }
        }

        // 清空列表
        scannedImages.clear();
        currentImageIndex = -1;

        // 更新 UI
        imageLabel.setIcon(null);
        imageLabel.setText("无图像");
        updateNavigationButtons();
        taskListPanel.setScannedImages(scannedImages); // 更新任务列表面板中的扫描图像引用
        statusLabel.setText("图像已清空");
    }

}
