@echo off
chcp 65001 > nul
echo ================================
echo 🚀 开始使用 Launch4j 打包可执行文件...
echo ================================

rem 切换到项目根目录（脚本的上一级目录）
cd /d %~dp0..
echo 当前路径为：%cd%
echo.

rem 设置 Launch4j 可执行文件路径
set LAUNCH4J_EXE=launch4jc.exe
set CONFIG_FILE=.\buildResouces\config.xml

where %LAUNCH4J_EXE% >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到 Launch4j：%LAUNCH4J_EXE%
    echo 请确认路径是否正确。
    echo.
    pause
    exit /b 1
)

rem 执行打包命令
"%LAUNCH4J_EXE%" "%CONFIG_FILE%"

rem 获取执行结果
set "RESULT=%errorlevel%"
echo.

if "%RESULT%"=="0" (
    echo ✅ 打包成功！可执行文件已生成。
) else (
    echo ❌ 打包失败，错误代码：%RESULT%
)

echo.
