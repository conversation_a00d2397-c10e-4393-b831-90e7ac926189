package com.yqc.englishessaygrader.ui;

import com.yqc.englishessaygrader.config.YqcServerConfig;
import okhttp3.*;
import org.json.JSONObject;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.util.Base64;
import java.util.concurrent.TimeUnit;

/**
 * 新增任务对话框
 */
public class TaskDialog extends JDialog {
    private JTextArea taskContentField;
    private JComboBox<String> taskScanTypeField;
    private JComboBox<String> taskGradeField;
    private JTextField taskNumberField;
    private JComboBox<String> taskDifficultyField;
    private JTextField taskWordNumMinField;
    private JTextField taskWordNumMaxField;
    private JTextField taskClassField;
    private JTextField remakeField;

    private JButton submitButton;
    private J<PERSON>abe<PERSON> statusLabel;
    private String accessToken;
    private OkHttpClient okHttpClient;

    public TaskDialog(JFrame parent, String accessToken) {
        super(parent, "新增任务", true);
        this.accessToken = accessToken;
        this.okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(120, TimeUnit.SECONDS)
                .readTimeout(120, TimeUnit.SECONDS)
                .writeTimeout(120, TimeUnit.SECONDS)
                .build();
        initUI();
    }

    private void initUI() {
        setSize(800, 750);
        setLocationRelativeTo(getOwner());

        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(new EmptyBorder(20, 20, 20, 20));

        JPanel formPanel = createFormPanel();
        JScrollPane scrollPane = new JScrollPane(formPanel);
        mainPanel.add(scrollPane, BorderLayout.CENTER);

        JPanel buttonPanel = createButtonPanel();
        mainPanel.add(buttonPanel, BorderLayout.SOUTH);

        statusLabel = new JLabel("请填写任务信息");
        mainPanel.add(statusLabel, BorderLayout.NORTH);

        setContentPane(mainPanel);
    }

    private JPanel createFormPanel() {
        JPanel panel = new JPanel(new BorderLayout(10, 10));

        JPanel contentPanel = createContentPanel();
        panel.add(contentPanel, BorderLayout.NORTH);

        JPanel fieldsPanel = createFieldsPanel();
        panel.add(fieldsPanel, BorderLayout.CENTER);

        return panel;
    }

    private JPanel createContentPanel() {
        JPanel panel = new JPanel(new BorderLayout(5, 5));
        panel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createEtchedBorder(), "作文题目要求",
            0, 0, new Font("Dialog", Font.BOLD, 14)));

        taskContentField = new JTextArea(12, 50);
        taskContentField.setLineWrap(true);
        taskContentField.setWrapStyleWord(true);
        taskContentField.setFont(new Font("Dialog", Font.PLAIN, 13));
        taskContentField.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        JScrollPane scrollPane = new JScrollPane(taskContentField);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);
        scrollPane.setPreferredSize(new Dimension(700, 250));

        panel.add(scrollPane, BorderLayout.CENTER);

        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        JButton chooseImageButton = new JButton("图片识别");
        chooseImageButton.addActionListener(e -> {
            JFileChooser fileChooser = new JFileChooser();
            int result = fileChooser.showOpenDialog(TaskDialog.this);
            if (result == JFileChooser.APPROVE_OPTION) {
                File selectedFile = fileChooser.getSelectedFile();
                String base64Image = fileToBase64(selectedFile);
                if (base64Image != null) {
                    identifyPhoto(base64Image);
                }
            }
        });
        buttonPanel.add(chooseImageButton);
        panel.add(buttonPanel, BorderLayout.SOUTH);

        return panel;
    }

    private JPanel createFieldsPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createEtchedBorder(), "任务信息",
            0, 0, new Font("Dialog", Font.BOLD, 14)));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        gbc.fill = GridBagConstraints.NONE;
        gbc.anchor = GridBagConstraints.WEST;

        gbc.gridx = 0;
        gbc.gridy = 0;
        panel.add(new JLabel("任务学段:"), gbc);

        String[] taskGrades = {"小学", "初中", "高中"};
        taskGradeField = new JComboBox<>(taskGrades);
        taskGradeField.setPreferredSize(new Dimension(100, 25));
        gbc.gridx = 1;
        panel.add(taskGradeField, gbc);

        gbc.gridx = 2;
        gbc.gridy = 0;
        panel.add(new JLabel("识别类型:"), gbc);

        String[] scanTypes = {"批改正面", "批改背面", "批改双面"};
        taskScanTypeField = new JComboBox<>(scanTypes);
        taskScanTypeField.setPreferredSize(new Dimension(100, 25));
        gbc.gridx = 3;
        panel.add(taskScanTypeField, gbc);

        gbc.gridx = 0;
        gbc.gridy = 1;
        panel.add(new JLabel("任务分数:"), gbc);

        taskNumberField = new JTextField(15);
        taskNumberField.setPreferredSize(new Dimension(125, 25));
        gbc.gridx = 1;
        panel.add(taskNumberField, gbc);

        gbc.gridx = 2;
        gbc.gridy = 1;
        panel.add(new JLabel("任务难度:"), gbc);

        String[] taskDifficulties = {"正常", "鼓励", "严格"};
        taskDifficultyField = new JComboBox<>(taskDifficulties);
        taskDifficultyField.setPreferredSize(new Dimension(100, 25));
        gbc.gridx = 3;
        panel.add(taskDifficultyField, gbc);

        gbc.gridx = 0;
        gbc.gridy = 2;
        panel.add(new JLabel("词数最小值:"), gbc);

        taskWordNumMinField = new JTextField(15);
        taskWordNumMinField.setPreferredSize(new Dimension(125, 25));
        gbc.gridx = 1;
        panel.add(taskWordNumMinField, gbc);

        gbc.gridx = 2;
        gbc.gridy = 2;
        panel.add(new JLabel("词数最大值:"), gbc);

        taskWordNumMaxField = new JTextField(15);
        taskWordNumMaxField.setPreferredSize(new Dimension(125, 25));
        gbc.gridx = 3;
        panel.add(taskWordNumMaxField, gbc);

        gbc.gridx = 0;
        gbc.gridy = 3;
        panel.add(new JLabel("任务班级:"), gbc);

        taskClassField = new JTextField(15);
        taskClassField.setPreferredSize(new Dimension(125, 25));
        gbc.gridx = 1;
        panel.add(taskClassField, gbc);

        gbc.gridx = 0;
        gbc.gridy = 4;
        gbc.gridwidth = 1;
        gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("备注:"), gbc);

        remakeField = new JTextField(50);
        gbc.gridx = 1;
        gbc.gridwidth = 3;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        panel.add(remakeField, gbc);

        gbc.weightx = 0.0;
        gbc.gridwidth = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;

        return panel;
    }

    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.RIGHT));

        submitButton = new JButton("提交");
        submitButton.addActionListener(e -> submitTask());
        panel.add(submitButton);

        return panel;
    }

    private void submitTask() {
        String taskContent = taskContentField.getText().trim()
                .replace("\\", "\\\\")
                .replace("\"", "\\\"")
                .replace("\n", "\\n");
        String taskGrade = taskGradeField.getSelectedItem().toString();
        String taskNumber = taskNumberField.getText().trim();
        String taskDifficulty = taskDifficultyField.getSelectedItem().toString();
        String taskWordNumMin = taskWordNumMinField.getText().trim();
        String taskWordNumMax = taskWordNumMaxField.getText().trim();
        String taskClass = taskClassField.getText().trim();
        String remake = remakeField.getText().trim();
        String taskScanType = taskScanTypeField.getSelectedItem().toString();

        String validationError = validateForm(taskContent, taskGrade, taskNumber, taskWordNumMin, taskWordNumMax);
        if (validationError != null) {
            JOptionPane.showMessageDialog(this, validationError, "提交错误", JOptionPane.ERROR_MESSAGE);
            return;
        }

        submitButton.setEnabled(false);
        statusLabel.setText("正在提交任务...");
        setCursor(Cursor.getPredefinedCursor(Cursor.WAIT_CURSOR));

        SwingWorker<Boolean, Void> worker = new SwingWorker<Boolean, Void>() {
            private String errorMessage = "";

            @Override
            protected Boolean doInBackground() throws Exception {
                try {
                    String mappedScanType = "";
                    if ("批改背面".equals(taskScanType)) {
                        mappedScanType = "双面识别";
                    } else if ("批改正面".equals(taskScanType)) {
                        mappedScanType = "单面识别";
                    }

                    MediaType JSON = MediaType.get("application/json; charset=utf-8");
                    String jsonInputString = String.format(
                            "{\"taskContent\": \"%s\", \"taskType\": \"%s\", \"taskGrade\": \"%s\", " +
                                    "\"taskNumber\": \"%s\", \"taskDifficulty\": \"%s\", \"taskWordNumMin\": \"%s\", " +
                                    "\"taskWordNumMax\": \"%s\", \"taskClass\": \"%s\", \"remake\": \"%s\", " +
                                    "\"taskScanType\": \"%s\"}",
                            taskContent, null, taskGrade, taskNumber, taskDifficulty,
                            taskWordNumMin, taskWordNumMax, taskClass, remake, mappedScanType);
                    RequestBody body = RequestBody.create(jsonInputString, JSON);

                    Request request = new Request.Builder()
                        .url(YqcServerConfig.getInstance().getUploadServerUrl()+"/paper/paperTask")
                        .post(body)
                        .addHeader("Content-Type", "application/json")
                        .addHeader("Authorization", "Bearer " + accessToken)
                        .build();

                    try (Response response = okHttpClient.newCall(request).execute()) {
                        if (response.isSuccessful() && response.body() != null) {
                            String jsonResponse = response.body().string();
                            System.out.println(jsonResponse);
                            return true;
                        } else {
                            errorMessage = "创建任务失败: " + response.code();
                            return false;
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    errorMessage = "创建任务过程发生错误: " + e.getMessage();
                    return false;
                }
            }

            @Override
            protected void done() {
                try {
                    boolean success = get();
                    if (success) {
                        JOptionPane.showMessageDialog(TaskDialog.this,
                            "任务创建成功", "成功", JOptionPane.INFORMATION_MESSAGE);
                        dispose();
                    } else {
                        JOptionPane.showMessageDialog(TaskDialog.this,
                            errorMessage.isEmpty() ? "创建任务失败" : errorMessage,
                            "创建失败", JOptionPane.ERROR_MESSAGE);
                        statusLabel.setText("创建任务失败");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    JOptionPane.showMessageDialog(TaskDialog.this,
                        "创建任务过程发生错误: " + e.getMessage(),
                        "错误", JOptionPane.ERROR_MESSAGE);
                    statusLabel.setText("创建任务错误");
                } finally {
                    submitButton.setEnabled(true);
                    setCursor(Cursor.getDefaultCursor());
                }
            }
        };

        worker.execute();
    }

    private String fileToBase64(File file) {
        try (FileInputStream fileInputStream = new FileInputStream(file);
             ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, bytesRead);
            }
            byte[] imageBytes = byteArrayOutputStream.toByteArray();
            return Base64.getEncoder().encodeToString(imageBytes);
        } catch (Exception e) {
            e.printStackTrace();
            JOptionPane.showMessageDialog(this, "图片转换失败", "错误", JOptionPane.ERROR_MESSAGE);
            return null;
        }
    }

    private void identifyPhoto(String base64Image) {
        submitButton.setEnabled(false);
        statusLabel.setText("正在识别图片...");
        setCursor(Cursor.getPredefinedCursor(Cursor.WAIT_CURSOR));

        SwingUtilities.invokeLater(() -> {
            JDialog progressDialog = new JDialog(this, "识别进度", false);
            progressDialog.setDefaultCloseOperation(JDialog.DO_NOTHING_ON_CLOSE);
            progressDialog.setSize(300, 100);
            progressDialog.setLocationRelativeTo(this);

            JProgressBar progressBar = new JProgressBar(0, 100);
            progressBar.setStringPainted(true);
            progressDialog.add(progressBar, BorderLayout.CENTER);
            progressDialog.setVisible(true);
            progressDialog.toFront();
            progressDialog.requestFocusInWindow();

            Timer timer = new Timer(1000, e -> {
                int value = progressBar.getValue();
                if (value < 100) {
                    progressBar.setValue(value + 1);
                }
            });

            SwingWorker<String, Void> worker = new SwingWorker<String, Void>() {
                private String errorMessage = "";

                @Override
                protected String doInBackground() throws Exception {
                    MediaType JSON = MediaType.get("application/json; charset=utf-8");
                    String jsonInputString = String.format("{\"imageBase64\": \"data:image/jpeg;base64,%s\"}", base64Image);
                    RequestBody body = RequestBody.create(jsonInputString, JSON);

                    Request request = new Request.Builder()
                            .url(YqcServerConfig.getInstance().getUploadServerUrl()+"/paper/paperTask/identifyPhoto")
                            .post(body)
                            .addHeader("Content-Type", "application/json")
                            .addHeader("Authorization", "Bearer " + accessToken)
                            .build();

                    try (Response response = okHttpClient.newCall(request).execute()) {
                        if (response.isSuccessful() && response.body() != null) {
                            return response.body().string();
                        } else {
                            errorMessage = "图片识别失败: " + response.code() + "请重试！";
                            return null;
                        }
                    }
                }

                @Override
                protected void done() {
                    try {
                        String responseString = get();
                        timer.stop();
                        progressDialog.dispose();

                        if (responseString != null) {
                            JSONObject jsonResponse = new JSONObject(responseString);
                            int code = jsonResponse.getInt("code");
                            String data = jsonResponse.getString("data");

                            if (code == 200) {
                                taskContentField.append(data);
                                statusLabel.setText("图片识别成功");
                            } else {
                                JOptionPane.showMessageDialog(TaskDialog.this,
                                        "图片识别失败: " + data,
                                        "识别失败", JOptionPane.ERROR_MESSAGE);
                                statusLabel.setText("图片识别失败");
                            }
                        } else {
                            JOptionPane.showMessageDialog(TaskDialog.this,
                                    errorMessage.isEmpty() ? "图片识别失败" : errorMessage,
                                    "识别失败", JOptionPane.ERROR_MESSAGE);
                            statusLabel.setText("图片识别失败");
                        }
                    } catch (Exception e) {
                        timer.stop();
                        progressDialog.dispose();
                        e.printStackTrace();
                        JOptionPane.showMessageDialog(TaskDialog.this,
                                "网络不稳定，请稍后重试！",
                                "错误", JOptionPane.ERROR_MESSAGE);
                        statusLabel.setText("图片识别错误");
                    } finally {
                        submitButton.setEnabled(true);
                        setCursor(Cursor.getDefaultCursor());
                    }
                }
            };

            timer.start();
            worker.execute();
        });
    }

    private String validateForm(String taskContent, String taskGrade, String taskNumber,
                               String taskWordNumMin, String taskWordNumMax) {
        if (taskContent.isEmpty()) {
            return "请填写作文题目要求";
        }

        if (taskGrade.isEmpty()) {
            return "请选择任务等级";
        }

        if (taskNumber.isEmpty()) {
            return "请填写任务分数（必填项）";
        }

        try {
            double score = Double.parseDouble(taskNumber);
            if (score < 0) {
                return "任务分数不能为负数";
            }
        } catch (NumberFormatException e) {
            return "任务分数必须是有效的数字";
        }

        Integer minWords = null;
        Integer maxWords = null;

        if (!taskWordNumMin.isEmpty()) {
            try {
                minWords = Integer.parseInt(taskWordNumMin);
                if (minWords < 0) {
                    return "词数最小值不能为负数";
                }
            } catch (NumberFormatException e) {
                return "词数最小值必须是有效的整数";
            }
        }

        if (!taskWordNumMax.isEmpty()) {
            try {
                maxWords = Integer.parseInt(taskWordNumMax);
                if (maxWords < 0) {
                    return "词数最大值不能为负数";
                }
            } catch (NumberFormatException e) {
                return "词数最大值必须是有效的整数";
            }
        }

        if (minWords != null && maxWords != null) {
            if (maxWords < minWords) {
                return "词数最大值必须大于等于最小值";
            }
        }

        return null;
    }
}
