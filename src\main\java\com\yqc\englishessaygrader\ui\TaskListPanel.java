package com.yqc.englishessaygrader.ui;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.Cursor;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

import javax.swing.DefaultCellEditor;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JProgressBar;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.ListSelectionModel;
import javax.swing.SwingUtilities;
import javax.swing.SwingWorker;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableCellRenderer;

import org.json.JSONObject;

import com.yqc.englishessaygrader.model.Task;
import com.yqc.englishessaygrader.scanner.ScannerController;
import com.yqc.englishessaygrader.service.TaskService;

/**
 * 任务列表面板
 * 用于显示和管理任务列表
 */
public class TaskListPanel extends JPanel {
    private JTable taskTable;
    private DefaultTableModel tableModel;
    private JButton refreshButton;
    private JLabel statusLabel;
    private String accessToken;
    private List<Task> tasks = new ArrayList<>();
    private List<Integer> selectedTaskIds;
    private ScannerController scannerController; // 添加扫描控制器引用
    private List<File> scannedImages; // 添加扫描图像列表引用
    private JButton addTaskButton;
    
    /**
     * 获取选中的任务ID列表
     * @return 选中的任务ID列表
     */
    public List<Integer> getSelectedTaskIds() {
        return selectedTaskIds;
    }
    
    /**
     * 设置扫描图像列表
     * @param scannedImages 扫描图像列表
     */
    public void setScannedImages(List<File> scannedImages) {
        this.scannedImages = scannedImages;
    }
    
    public TaskListPanel(String accessToken, ScannerController scannerController) {
        this.accessToken = accessToken;
        this.scannerController = scannerController; // 初始化扫描控制器
        this.tasks = new ArrayList<>();
        this.selectedTaskIds = new ArrayList<>();
        this.scannedImages = new ArrayList<>(); // 初始化扫描图像列表
        initUI();
        loadTaskList();
    }
    
    private void initUI() {
        setLayout(new BorderLayout(5, 5));

        // 创建表格模型 - 包含ID、日期、题目要求、班级、备注信息、查看、上传、删除
        String[] columnNames = {"ID", "日期", "题目要求", "班级", "备注信息", "查看", "上传", "删除"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                // 只有操作列（查看、上传、删除）可编辑
                return column >= 5;
            }

            @Override
            public Class<?> getColumnClass(int columnIndex) {
                if (columnIndex >= 5) {
                    return JButton.class; // 操作列使用按钮
                }
                return Object.class;
            }
        };
        
        // 创建表格并设置美观的样式
        taskTable = new JTable(tableModel);
        taskTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        taskTable.setAutoResizeMode(JTable.AUTO_RESIZE_ALL_COLUMNS);
        taskTable.setRowHeight(30); // 增加行高
        taskTable.setIntercellSpacing(new Dimension(5, 5)); // 增加单元格间距
        taskTable.setShowGrid(true); // 显示网格线
        taskTable.setGridColor(new Color(230, 230, 230)); // 设置网格线颜色
        
        // 设置表头样式
        taskTable.getTableHeader().setFont(new Font("Dialog", Font.BOLD, 12));
        taskTable.getTableHeader().setBackground(new Color(240, 240, 240));
        taskTable.getTableHeader().setForeground(new Color(50, 50, 50));
        taskTable.getTableHeader().setPreferredSize(new Dimension(0, 30)); // 设置表头高度

        // 设置列宽并隐藏ID列
        taskTable.getColumnModel().getColumn(0).setMinWidth(0);  // ID列最小宽度为0
        taskTable.getColumnModel().getColumn(0).setMaxWidth(0);  // ID列最大宽度也为0
        taskTable.getColumnModel().getColumn(0).setWidth(0);     // 设置宽度为0

        taskTable.getColumnModel().getColumn(1).setPreferredWidth(100);  // 日期
        taskTable.getColumnModel().getColumn(2).setPreferredWidth(200); // 题目要求
        taskTable.getColumnModel().getColumn(3).setPreferredWidth(100); // 班级
        taskTable.getColumnModel().getColumn(4).setPreferredWidth(150); // 备注信息
        taskTable.getColumnModel().getColumn(5).setPreferredWidth(60);  // 查看
        taskTable.getColumnModel().getColumn(6).setPreferredWidth(60);  // 上传
        taskTable.getColumnModel().getColumn(7).setPreferredWidth(60);  // 删除

        // 使用 ButtonEditor 作为单元格编辑器
        for (int i = 5; i <= 7; i++) {
            taskTable.getColumnModel().getColumn(i).setCellRenderer(new ButtonRenderer());
            taskTable.getColumnModel().getColumn(i).setCellEditor(new ButtonEditor(new JCheckBox(), this));
        }

        // 行选择监听器
        taskTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                int selectedRow = taskTable.getSelectedRow();
                if (selectedRow >= 0) {
                    int taskId = (int) tableModel.getValueAt(selectedRow, 0); // ID是第一列
                    if (!selectedTaskIds.contains(taskId)) {
                        selectedTaskIds.clear(); // 单选模式
                        selectedTaskIds.add(taskId);
                    }
                }
            }
        });

        // 双击事件监听器 - 双击任务列表打开任务详情
        taskTable.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2) { // 双击事件
                    int selectedRow = taskTable.getSelectedRow();
                    if (selectedRow >= 0) {
                        int taskId = (int) tableModel.getValueAt(selectedRow, 0); // 获取任务ID
                        try {
                            showTaskDetails(taskId);
                        } catch (Exception ex) {
                            JOptionPane.showMessageDialog(TaskListPanel.this,
                                    "网络不佳，请稍后重试", "查看失败", JOptionPane.ERROR_MESSAGE);
                            ex.printStackTrace();
                        }
                    }
                }
            }
        });
        
        // 创建表格滚动面板
        JScrollPane scrollPane = new JScrollPane(taskTable);
        add(scrollPane, BorderLayout.CENTER);
        
        // 创建按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        
        refreshButton = new JButton("刷新列表");
        refreshButton.addActionListener(e -> loadTaskList());
        buttonPanel.add(refreshButton);
        
        addTaskButton = new JButton("新增任务");
        addTaskButton.addActionListener(e -> showAddTaskDialog());
        buttonPanel.add(addTaskButton);
        
        add(buttonPanel, BorderLayout.SOUTH);
        
        // 创建状态标签
        statusLabel = new JLabel("加载任务列表...");
        add(statusLabel, BorderLayout.NORTH);
    }
    
    /**
     * 加载任务列表
     */
    public void loadTaskList() {
        statusLabel.setText("正在加载任务列表...");
        setCursor(Cursor.getPredefinedCursor(Cursor.WAIT_CURSOR));
        
        // 清空表格
        tableModel.setRowCount(0);
        
        // 在后台线程中加载任务列表
        SwingWorker<List<Task>, Void> worker = new SwingWorker<List<Task>, Void>() {
            @Override
            protected List<Task> doInBackground() throws Exception {
                return TaskService.getTaskList(accessToken);
            }

            @Override
            protected void done() {
                try {
                    tasks = get();
                    if (tasks != null && !tasks.isEmpty()) {
                        // 清空表格
                        tableModel.setRowCount(0);

                        // 更新表格数据
                        for (Task task : tasks) {
                            Object[] rowData = new Object[8];
                            rowData[0] = task.getTaskId(); // ID (hidden)
                            rowData[1] = task.getCreateTime(); // 日期
                            rowData[2] = task.getTaskContent(); // 题目要求
                            rowData[3] = task.getTaskClass(); // 班级
                            rowData[4] = task.getRemake() == null ? "" : task.getRemake(); // 备注信息
                            rowData[5] = "查看"; // 查看按钮文本
                            rowData[6] = "上传"; // 上传按钮文本
                            rowData[7] = "删除"; // 删除按钮文本
                            tableModel.addRow(rowData);
                        }
                        statusLabel.setText("已加载 " + tasks.size() + " 个任务");
                    } else {
                        statusLabel.setText("没有找到任务");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    System.out.println("加载任务列表失败: " + e.getMessage());
                    statusLabel.setText("加载任务列表失败: " + e.getMessage());

                } finally {
                    setCursor(Cursor.getDefaultCursor());
                }
            }

        };
        
        worker.execute();
    }
    
    /**
     * 显示添加任务对话框
     */
    private void showAddTaskDialog() {
        TaskDialog dialog = new TaskDialog((JFrame) SwingUtilities.getWindowAncestor(this), accessToken);
        dialog.setVisible(true);

        // 对话框关闭后刷新任务列表
        loadTaskList();
    }

    /**
     * 显示任务详情对话框
     * @param taskId 任务ID
     * @throws Exception 网络异常
     */
    public void showTaskDetails(int taskId) throws Exception {
        JSONObject taskInfo = TaskService.getTaskById(taskId, accessToken);
        // 显示自定义窗口
        JFrame frame = (JFrame) SwingUtilities.getWindowAncestor(this);
        TaskDetailsDialog dialog = new TaskDetailsDialog(frame, taskInfo);
        dialog.setVisible(true); // 弹出窗口
    }
    
    /**
     * 按钮渲染器
     */
    class ButtonRenderer extends JButton implements TableCellRenderer {
        public ButtonRenderer() {
            setOpaque(true);
        }
        
        @Override
        public Component getTableCellRendererComponent(JTable table, Object value,
                boolean isSelected, boolean hasFocus, int row, int column) {
            setText((value == null) ? "" : value.toString());
            return this;
        }
    }

    /**
     * 按钮编辑器
     */
    class ButtonEditor extends DefaultCellEditor {
        protected JButton button;
        private String label;
        private boolean isPushed;
        private final TaskListPanel taskListPanel; // 外部面板引用

        public ButtonEditor(JCheckBox checkBox, TaskListPanel panel) {
            super(checkBox);
            this.taskListPanel = panel;
            button = new JButton();
            button.setOpaque(true);
            button.addActionListener(e -> fireEditingStopped());
        }

        @Override
        public Component getTableCellEditorComponent(JTable table, Object value,
                                                     boolean isSelected, int row, int column) {
            label = (value == null) ? "" : value.toString();
            button.setText(label);
            isPushed = true;
            return button;
        }

        @Override
        public Object getCellEditorValue() {
            if (isPushed) {
                int row = taskTable.getSelectedRow();
                if (row >= 0) {
                    int taskId = (int) tableModel.getValueAt(row, 0); // 获取任务ID
                    String buttonText = label;

                    if ("查看".equals(buttonText)) {
                        try {
                            showTaskDetails(taskId);
                        } catch (Exception e) {
                            JOptionPane.showMessageDialog(TaskListPanel.this,
                                    "网络不佳，请稍后重试", "查看失败", JOptionPane.ERROR_MESSAGE);
                            throw new RuntimeException(e);
                        }
                    } else if ("上传".equals(buttonText)) {
                        uploadTaskImages(taskId);
                    } else if ("删除".equals(buttonText)) {
                        try {
                            deleteTask(taskId);
                        } catch (Exception e) {
                            JOptionPane.showMessageDialog(TaskListPanel.this,
                                    "网络不佳，请稍后重试", "删除失败", JOptionPane.ERROR_MESSAGE);
                            throw new RuntimeException(e);
                        }
                    }
                }
            }
            isPushed = false;
            return label;
        }

        private void showTaskDetails(int taskId) throws Exception {
            JSONObject taskInfo = TaskService.getTaskById(taskId, accessToken);
            // 显示自定义窗口
            JFrame frame = (JFrame) SwingUtilities.getWindowAncestor(taskListPanel);
            TaskDetailsDialog dialog = new TaskDetailsDialog(frame, taskInfo);
            dialog.setVisible(true); // 弹出窗口
        }

        private void uploadTaskImages(int taskId) {
            if (scannedImages == null || scannedImages.isEmpty()) {
                JOptionPane.showMessageDialog(TaskListPanel.this,
                        "没有可上传试卷，请先扫描文档", "上传错误", JOptionPane.ERROR_MESSAGE);
            } else {
                String[] options = {"上传试卷", "取消"};
                int choice = JOptionPane.showOptionDialog(TaskListPanel.this,
                        "确认是否要上传？", "上传试卷",
                        JOptionPane.DEFAULT_OPTION, JOptionPane.QUESTION_MESSAGE,
                        null, options, options[0]);

                if (choice == 0) {
                    JFrame frame = (JFrame) SwingUtilities.getWindowAncestor(TaskListPanel.this);
                    if (frame instanceof MainFrame) {
                        try {
                            ((MainFrame) frame).uploadAllImages(taskId);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                }
            }
        }

        private void deleteTask(int taskId) throws Exception {
            int confirm = JOptionPane.showConfirmDialog(TaskListPanel.this,
                    "确定要删除任务", "删除任务",
                    JOptionPane.YES_NO_OPTION);
            if (confirm == JOptionPane.YES_OPTION) {
                new SwingWorker<Void, Void>() {
                    @Override
                    protected Void doInBackground() throws Exception {
                        TaskService.deleteTask(taskId, accessToken);
                        return null;
                    }

                    @Override
                    protected void done() {
                        taskTable.clearSelection(); // 清除选中行
                        loadTaskList(); // 刷新任务列表
                    }
                }.execute();
            }
        }

        @Override
        public boolean stopCellEditing() {
            isPushed = false;
            return super.stopCellEditing();
        }
    }
    
}