package com.yqc.englishessaygrader.model;

/**
 * 任务模型类
 * 用于表示任务信息
 */
public class Task {
    private int taskId;
    private String taskContent; // 作文题目要求
    private String taskType;   // 任务类型/作文种类
    private String taskGrade;  // 任务等级/学段
    private String taskNumber; // 任务分数/总分
    private String taskDifficulty; // 任务难度/评分
    private String taskWordNumMin; // 任务词数限制最小值
    private String taskWordNumMax; // 任务词数限制最大值
    private String taskClass;  // 任务班级/备注
    private String remake;     // 备注
    private String createTime; // 任务创建时间
    
    // 构造函数
    public Task() {
    }
    
    // Getters and Setters
    public int getTaskId() {
        return taskId;
    }
    
    public void setTaskId(int taskId) {
        this.taskId = taskId;
    }
    
    public String getTaskContent() {
        return taskContent;
    }
    
    public void setTaskContent(String taskContent) {
        this.taskContent = taskContent;
    }
    
    public String getTaskType() {
        return taskType;
    }
    
    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }
    
    public String getTaskGrade() {
        return taskGrade;
    }
    
    public void setTaskGrade(String taskGrade) {
        this.taskGrade = taskGrade;
    }
    
    public String getTaskNumber() {
        return taskNumber;
    }
    
    public void setTaskNumber(String taskNumber) {
        this.taskNumber = taskNumber;
    }
    
    public String getTaskDifficulty() {
        return taskDifficulty;
    }
    
    public void setTaskDifficulty(String taskDifficulty) {
        this.taskDifficulty = taskDifficulty;
    }
    
    public String getTaskWordNumMin() {
        return taskWordNumMin;
    }
    
    public void setTaskWordNumMin(String taskWordNumMin) {
        this.taskWordNumMin = taskWordNumMin;
    }
    
    public String getTaskWordNumMax() {
        return taskWordNumMax;
    }
    
    public void setTaskWordNumMax(String taskWordNumMax) {
        this.taskWordNumMax = taskWordNumMax;
    }
    
    public String getTaskClass() {
        return taskClass;
    }
    
    public void setTaskClass(String taskClass) {
        this.taskClass = taskClass;
    }
    
    public String getRemake() {
        return remake == null ? "" : remake;
    }
    
    public void setRemake(String remake) {
        this.remake = remake != null ? remake : "";
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
}