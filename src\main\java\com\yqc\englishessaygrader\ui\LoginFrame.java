package com.yqc.englishessaygrader.ui;

import com.yqc.englishessaygrader.config.YqcServerConfig;
import com.yqc.englishessaygrader.config.UserPreferences;
import com.yqc.englishessaygrader.scanner.ScannerController;
import com.yqc.englishessaygrader.service.TaskService;
import com.yqc.englishessaygrader.utils.VersionUtil;
import okhttp3.*;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 登录界面
 * 用户必须成功登录后才能进入主界面
 */
public class LoginFrame extends JFrame {
    private JTextField usernameField;
    private JPasswordField passwordField;
    private JCheckBox showPasswordCheckBox;
    private JCheckBox rememberPasswordCheckBox;
    private JButton loginButton;
    private JLabel statusLabel;

    // 标志：密码是否来自保存的凭据（未被用户修改）
    private boolean passwordFromSaved = false;
    
    private ScannerController scannerController;

    private OkHttpClient okHttpClient;

    private TaskService taskService;
    
    // 保存登录成功后的access_token
    private static String accessToken;
    
    public LoginFrame(ScannerController scannerController,TaskService taskService) {
        this.scannerController = scannerController;
        this.okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(120, TimeUnit.SECONDS)
                .readTimeout(120, TimeUnit.SECONDS)
                .writeTimeout(120, TimeUnit.SECONDS)
                .build();
        this.taskService = taskService;
        initUI();
    }
    
    private void initUI() {
        String version = VersionUtil.getVersion();
        setTitle("YQC 英语批改助手 - 登录 (v" + version + ")");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(450, 350);
        setLocationRelativeTo(null);
        
        // 创建主面板
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(new EmptyBorder(20, 20, 20, 20));
        
        // 创建登录面板
        JPanel loginPanel = createLoginPanel();
        mainPanel.add(loginPanel, BorderLayout.CENTER);
        
        // 创建状态栏
        statusLabel = new JLabel("请输入用户名和密码");
        mainPanel.add(statusLabel, BorderLayout.SOUTH);
        
        // 设置内容面板
        setContentPane(mainPanel);

        // 加载保存的登录信息
        loadSavedLoginInfo();
    }
    
    private JPanel createLoginPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();

        // 添加标题标签
        JLabel titleLabel = new JLabel("用户登录", JLabel.CENTER);
        titleLabel.setFont(new Font("Dialog", Font.BOLD, 18));
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.gridwidth = 2;
        gbc.insets = new Insets(10, 10, 30, 10);
        gbc.anchor = GridBagConstraints.CENTER;
        panel.add(titleLabel, gbc);

        // 用户名标签
        gbc.gridx = 0;
        gbc.gridy = 1;
        gbc.gridwidth = 1;
        gbc.insets = new Insets(5, 10, 5, 10);
        gbc.anchor = GridBagConstraints.EAST;
        gbc.fill = GridBagConstraints.NONE;
        JLabel usernameLabel = new JLabel("用户名:");
        usernameLabel.setFont(new Font("Dialog", Font.PLAIN, 14));
        panel.add(usernameLabel, gbc);

        // 用户名输入框
        usernameField = new JTextField(20);
        usernameField.setPreferredSize(new Dimension(200, 25));
        gbc.gridx = 1;
        gbc.gridy = 1;
        gbc.insets = new Insets(5, 5, 5, 10);
        gbc.anchor = GridBagConstraints.WEST;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        panel.add(usernameField, gbc);

        // 密码标签
        gbc.gridx = 0;
        gbc.gridy = 2;
        gbc.insets = new Insets(5, 10, 5, 10);
        gbc.anchor = GridBagConstraints.EAST;
        gbc.fill = GridBagConstraints.NONE;
        JLabel passwordLabel = new JLabel("密码:");
        passwordLabel.setFont(new Font("Dialog", Font.PLAIN, 14));
        panel.add(passwordLabel, gbc);

        // 密码输入框
        passwordField = new JPasswordField(20);
        passwordField.setPreferredSize(new Dimension(200, 25));
        // 添加文档监听器，当密码改变时重新启用显示密码功能
        passwordField.getDocument().addDocumentListener(new javax.swing.event.DocumentListener() {
            @Override
            public void insertUpdate(javax.swing.event.DocumentEvent e) {
                onPasswordChanged();
            }

            @Override
            public void removeUpdate(javax.swing.event.DocumentEvent e) {
                onPasswordChanged();
            }

            @Override
            public void changedUpdate(javax.swing.event.DocumentEvent e) {
                onPasswordChanged();
            }
        });
        gbc.gridx = 1;
        gbc.gridy = 2;
        gbc.insets = new Insets(5, 5, 5, 10);
        gbc.anchor = GridBagConstraints.WEST;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        panel.add(passwordField, gbc);

        // 创建勾选框面板
        JPanel checkBoxPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));

        // 显示密码勾选框
        showPasswordCheckBox = new JCheckBox("显示密码");
        showPasswordCheckBox.setFont(new Font("Dialog", Font.PLAIN, 12));
        showPasswordCheckBox.addActionListener(e -> {
            // 只有在没有加载保存的密码时才允许显示密码
            if (!isPasswordFromSaved()) {
                if (showPasswordCheckBox.isSelected()) {
                    passwordField.setEchoChar((char) 0);  // 显示密码
                } else {
                    passwordField.setEchoChar('*');  // 隐藏密码
                }
            } else {
                // 如果是保存的密码，不允许显示，重置勾选框状态
                showPasswordCheckBox.setSelected(false);
                JOptionPane.showMessageDialog(this, "出于安全考虑，无法显示已保存的密码", "提示", JOptionPane.INFORMATION_MESSAGE);
            }
        });
        checkBoxPanel.add(showPasswordCheckBox);

        // 添加间距
        checkBoxPanel.add(Box.createHorizontalStrut(20));

        // 记住密码勾选框
        rememberPasswordCheckBox = new JCheckBox("记住密码");
        rememberPasswordCheckBox.setFont(new Font("Dialog", Font.PLAIN, 12));
        checkBoxPanel.add(rememberPasswordCheckBox);

        gbc.gridx = 1;
        gbc.gridy = 3;
        gbc.gridwidth = 1;
        gbc.insets = new Insets(5, 5, 15, 10);
        gbc.anchor = GridBagConstraints.WEST;
        gbc.fill = GridBagConstraints.NONE;
        panel.add(checkBoxPanel, gbc);

        // 登录按钮
        loginButton = new JButton("登录");
        loginButton.setFont(new Font("Dialog", Font.BOLD, 14));
        loginButton.setPreferredSize(new Dimension(100, 35));
        loginButton.addActionListener(e -> login());
        gbc.gridx = 0;
        gbc.gridy = 4;
        gbc.gridwidth = 2;
        gbc.insets = new Insets(20, 10, 10, 10);
        gbc.anchor = GridBagConstraints.CENTER;
        gbc.fill = GridBagConstraints.NONE;
        panel.add(loginButton, gbc);

        return panel;
    }

    /**
     * 加载保存的登录信息
     */
    private void loadSavedLoginInfo() {
        UserPreferences prefs = UserPreferences.getInstance();

        // 加载用户名
        String savedUsername = prefs.getSavedUsername();
        if (!savedUsername.isEmpty()) {
            usernameField.setText(savedUsername);
        }

        // 如果启用了记住密码功能，加载密码
        if (prefs.isRememberPasswordEnabled()) {
            String savedPassword = prefs.getSavedPassword();
            if (!savedPassword.isEmpty()) {
                passwordField.setText(savedPassword);
                rememberPasswordCheckBox.setSelected(true);
                // 禁用显示密码功能，因为这是保存的密码
                showPasswordCheckBox.setEnabled(false);
                showPasswordCheckBox.setToolTipText("出于安全考虑，无法显示已保存的密码");
            }
        }
    }

    /**
     * 判断当前密码是否来自保存的凭据
     */
    private boolean isPasswordFromSaved() {
        UserPreferences prefs = UserPreferences.getInstance();
        if (!prefs.isRememberPasswordEnabled()) {
            return false;
        }

        String currentPassword = new String(passwordField.getPassword());
        String savedPassword = prefs.getSavedPassword();

        return !savedPassword.isEmpty() && currentPassword.equals(savedPassword);
    }

    /**
     * 密码字段内容改变时的处理方法
     */
    private void onPasswordChanged() {
        // 如果用户修改了密码，重新启用显示密码功能
        if (!isPasswordFromSaved()) {
            showPasswordCheckBox.setEnabled(true);
            showPasswordCheckBox.setToolTipText(null);
        }
    }
    
    private void login() {
    String username = usernameField.getText().trim();
    String password = new String(passwordField.getPassword());

    if (username.isEmpty() || password.isEmpty()) {
        JOptionPane.showMessageDialog(this,
            "请输入用户名和密码", "登录错误", JOptionPane.ERROR_MESSAGE);
        return;
    }

    // 禁用登录按钮
    loginButton.setEnabled(false);
    statusLabel.setText("正在登录...");
    setCursor(Cursor.getPredefinedCursor(Cursor.WAIT_CURSOR));

    // 在后台线程中执行登录
    SwingWorker<Boolean, Void> worker = new SwingWorker<Boolean, Void>() {
        private String errorMessage = "";

        @Override
        protected Boolean doInBackground() throws Exception {
            try {

                // 构建 JSON 请求体
                MediaType JSON = MediaType.get("application/json; charset=utf-8");
                String jsonInputString = String.format("{\"username\": \"%s\", \"password\": \"%s\"}",
                                                      username, password);
                RequestBody body = RequestBody.create(jsonInputString, JSON);

                // 创建请求
                Request request = new Request.Builder()
                    .url(YqcServerConfig.getInstance().getUploadServerUrl()+"/auth/yqcAuth/yqcLogin")
                    .post(body)
                    .build();

                // 发送请求并获取响应
                try (Response response = okHttpClient.newCall(request).execute()) {
                    if (response.isSuccessful() && response.body() != null) {
                        // 读取响应内容
                        String jsonResponse = response.body().string();

                        // 解析响应 JSON，检查登录是否成功
                        Map<String, Object> responseMap = parseJson(jsonResponse);

                        // 检查响应码
                        if (responseMap.containsKey("code") && (int)responseMap.get("code") == 200) {
                            // 登录成功，保存 access_token
                            if (responseMap.containsKey("data") && responseMap.get("data") instanceof Map) {
                                Map<String, Object> dataMap = (Map<String, Object>) responseMap.get("data");
                                if (dataMap.containsKey("access_token")) {
                                    accessToken = (String) dataMap.get("access_token");
                                    System.out.println("成功获取 access_token: " + accessToken.substring(0, Math.min(10, accessToken.length())) + "...");
                                } else {
                                    System.out.println("响应中没有 access_token 字段");
                                }
                            }
                            return true;
                        } else {
                            // 登录失败，获取错误信息
                            errorMessage = responseMap.containsKey("msg") ?
                                (String)responseMap.get("msg") : "登录失败，请检查用户名和密码";
                            return false;
                        }
                    } else {
                        // 登录失败
                        errorMessage = "登录失败: " + response.code();
                        return false;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                errorMessage = "登录过程发生错误: " + e.getMessage();
                return false;
            }
        }

        @Override
        protected void done() {
            try {
                boolean success = get();
                if (success) {
                    // 登录成功，保存用户凭据（如果用户选择记住密码）
                    if (rememberPasswordCheckBox.isSelected()) {
                        UserPreferences.getInstance().saveLoginInfo(username, password, true);
                    } else {
                        // 只保存用户名，不保存密码
                        UserPreferences.getInstance().saveUsername(username);
                        UserPreferences.getInstance().setRememberPassword(false);
                    }

                    // 登录成功，打开主界面
                    statusLabel.setText("登录成功，正在打开主界面...");
                    openMainFrame();
                } else {
                    // 登录失败
                    JOptionPane.showMessageDialog(LoginFrame.this,
                        errorMessage.isEmpty() ? "用户名或密码错误" : errorMessage,
                        "登录失败", JOptionPane.ERROR_MESSAGE);
                    statusLabel.setText("登录失败");
                }
            } catch (Exception e) {
                e.printStackTrace();
                JOptionPane.showMessageDialog(LoginFrame.this,
                    "登录过程发生错误: " + e.getMessage(),
                    "错误", JOptionPane.ERROR_MESSAGE);
                statusLabel.setText("登录错误");
            } finally {
                // 恢复 UI
                loginButton.setEnabled(true);
                setCursor(Cursor.getDefaultCursor());
            }
        }
    };

    worker.execute();
}

    
    private void openMainFrame() {
        // 创建并显示主窗口
        MainFrame mainFrame = new MainFrame(scannerController, accessToken,  taskService);
        mainFrame.setVisible(true);
        
        // 关闭登录窗口
        this.dispose();
    }
    
    /**
     * 获取登录成功后的access_token
     * @return access_token字符串
     */
    public static String getAccessToken() {
        return accessToken;
    }
    
    /**
     * 简单解析JSON字符串
     * @param jsonString JSON字符串
     * @return 解析后的Map对象
     */
    private Map<String, Object> parseJson(String jsonString) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 移除可能的首尾括号
            String json = jsonString.trim();
            if (json.startsWith("{") && json.endsWith("}")) {
                json = json.substring(1, json.length() - 1);
            }
            
            // 分割键值对，但要处理嵌套的JSON对象
            StringBuilder currentPair = new StringBuilder();
            int braceCount = 0;
            char[] chars = json.toCharArray();
            
            for (int i = 0; i < chars.length; i++) {
                char c = chars[i];
                
                if (c == '{') {
                    braceCount++;
                } else if (c == '}') {
                    braceCount--;
                }
                
                // 只有在不在嵌套对象内部时，才以逗号作为分隔符
                if (c == ',' && braceCount == 0) {
                    processPair(currentPair.toString().trim(), result);
                    currentPair = new StringBuilder();
                } else {
                    currentPair.append(c);
                }
            }
            
            // 处理最后一个键值对
            if (currentPair.length() > 0) {
                processPair(currentPair.toString().trim(), result);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return result;
    }
    
    /**
     * 处理单个键值对
     */
    private void processPair(String pair, Map<String, Object> result) {
        String[] keyValue = pair.split(":", 2);
        if (keyValue.length == 2) {
            String key = keyValue[0].trim();
            // 移除键的引号
            if (key.startsWith("\"") && key.endsWith("\"")) {
                key = key.substring(1, key.length() - 1);
            }
            
            String value = keyValue[1].trim();
            
            // 处理不同类型的值
            if (value.equals("null")) {
                result.put(key, null);
            } else if (value.startsWith("\"") && value.endsWith("\"")) {
                // 字符串值
                result.put(key, value.substring(1, value.length() - 1));
            } else if (value.equals("true")) {
                result.put(key, true);
            } else if (value.equals("false")) {
                result.put(key, false);
            } else if (value.startsWith("{") && value.endsWith("}")) {
                // 嵌套的JSON对象
                result.put(key, parseJson(value));
            } else {
                try {
                    // 尝试解析为整数
                    result.put(key, Integer.parseInt(value));
                } catch (NumberFormatException e) {
                    try {
                        // 尝试解析为浮点数
                        result.put(key, Double.parseDouble(value));
                    } catch (NumberFormatException e2) {
                        // 如果不是数字，保留原始字符串
                        result.put(key, value);
                    }
                }
            }
        }
    }
}