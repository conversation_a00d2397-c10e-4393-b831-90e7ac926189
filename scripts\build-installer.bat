@echo off
chcp 65001 > nul
echo ========================================
echo 🛠️  开始使用 Inno Setup 编译安装包...
echo ========================================

rem 切换到项目根目录（脚本的上一级目录）
cd /d %~dp0..
echo 当前路径为：%cd%
echo.

rem 设置 Inno Setup 编译器路径
set INNO_SETUP_EXE=ISCC.exe
set ISS_FILE=.\buildResouces\setup.iss

rem 检查 Inno Setup 是否存在
where %INNO_SETUP_EXE% >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到 Inno Setup 编译器：%INNO_SETUP_EXE%
    echo 请确认路径是否正确。
    echo.
    pause
    exit /b 1
)

rem 判断是否通过参数传入版本号
if "%~1"=="" (
    rem 没有传入参数，交互输入
    set /p VERSION=请输入版本号（例如 1.0.0）: 
) else (
    rem 使用传入的第一个参数作为版本号
    set VERSION=%~1
)

echo.
echo 🔧 开始编译，版本号为：%VERSION%
echo.

rem 执行编译命令
%INNO_SETUP_EXE% /dVersion="%VERSION%" "%ISS_FILE%"

rem 获取执行结果
set "RESULT=%errorlevel%"
echo.

if "%RESULT%"=="0" (
    echo ✅ 编译成功！安装包已生成在：%cd%/dist 路径下
) else (
    echo ❌ 编译失败，错误代码：%RESULT%
)

echo.
