@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

:: 最小文件大小阈值（字节），小于此值认为下载失败
set "MIN_SIZE=2048"

:: 配置下载目标目录
set "TARGET_DIR=%~dp0..\driver"
mkdir "%TARGET_DIR%" >nul 2>&1
cd /d "%TARGET_DIR%"

echo 📥 开始批量下载...

for %%U in (
    "https://download.microsoft.com/download/f/d/b/fdb0e76d-2c15-45d1-a49b-bfb405008569/Windows6.1-*********-x64.msu"
    "https://download.microsoft.com/download/8/b/7/8b79adc2-162c-4cf4-a200-3aeaadc455bf/NDP462-*********-x86-x64-AllOS-ENU.exe"
    "http://ftp.gfi-bremen.de/fi-7xxx/PSIPTWAIN-1_60_0.exe"
) do (
    set "URL=%%~U"
    for %%F in ("%%~nxU") do (
        set "FILENAME=%%~nxF"
    )

    set "NEED_DOWNLOAD=1"

    if exist "!FILENAME!" (
        for %%S in ("!FILENAME!") do set "SIZE=%%~zS"
        if !SIZE! GEQ !MIN_SIZE! (
            echo ✅ 已存在且大小合理，跳过：!FILENAME! （大小 !SIZE! 字节）
            set "NEED_DOWNLOAD=0"
        ) else (
            echo ⚠️ 文件存在但太小，重新下载：!FILENAME! （大小 !SIZE! 字节）
            del "!FILENAME!" >nul
        )
    )

    if !NEED_DOWNLOAD! == 1 (
        echo 🌐 正在下载：!FILENAME!

        echo !URL! | findstr /i "smartprinter.co.il" >nul
        if !errorlevel! == 0 (
            curl -L -o "!FILENAME!" ^
                -H "User-Agent: Mozilla/5.0" ^
                -H "Referer: http://www.smartprinter.co.il/" ^
                "!URL!"
        ) else (
            curl -L -o "!FILENAME!" "!URL!"
        )

        if exist "!FILENAME!" (
            for %%S in ("!FILENAME!") do set "SIZE=%%~zS"
            if !SIZE! GEQ !MIN_SIZE! (
                echo ✅ 下载完成：!FILENAME!
            ) else (
                echo ❌ 文件太小，可能下载失败：!FILENAME! （大小 !SIZE! 字节）
                del "!FILENAME!" >nul
            )
        ) else (
            echo ❌ 下载失败：!FILENAME!
        )
    )
)

echo.
echo 🎉 所有文件下载处理完成！
endlocal
