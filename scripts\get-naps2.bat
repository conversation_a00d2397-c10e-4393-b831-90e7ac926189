@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

:: 配置版本与路径
set "NAPS_VERSION=7.5.3"
set "NAPS_ZIP=naps2-%NAPS_VERSION%-win.zip"
set "NAPS_URL=https://github.com/cyanfish/naps2/releases/download/v%NAPS_VERSION%/%NAPS_ZIP%"
set "TARGET_DIR=%~dp0../utils"
set "NAPS_DIR=%TARGET_DIR%\naps2"

:: 创建 target 目录
mkdir "%TARGET_DIR%" >nul 2>&1
cd /d "%TARGET_DIR%"

:: 如果已存在 EXE，跳过下载
if exist "%NAPS_DIR%\App\NAPS2.Console.exe" (
    echo ✅ NAPS2 已存在：%NAPS_DIR%
    goto end
)

:: 下载 NAPS2
echo 📥 正在下载 NAPS2：%NAPS_ZIP%
curl -L -o "%NAPS_ZIP%" "%NAPS_URL%"
if not exist "%NAPS_ZIP%" (
    echo ❌ 下载失败，文件未找到：%NAPS_ZIP%
    goto end
)

:: 解压 ZIP
echo 📂 正在解压...
powershell -Command "Expand-Archive -Path '%NAPS_ZIP%' -DestinationPath 'naps2-temp'" 2>nul

:: 查找是否成功
if not exist "naps2-temp\App\NAPS2.Console.exe" (
    echo ❌ 解压失败，未找到 naps2.exe
    goto end
)

:: 移动目录
move "naps2-temp" "naps2" >nul 2>&1
del "%NAPS_ZIP%"

:end
echo.
if exist "%NAPS_DIR%\App\NAPS2.Console.exe" (
    echo ✅ NAPS2 准备完成！路径：%NAPS_DIR%
) else (
    echo ❌ NAPS2 下载或解压失败。
)

endlocal
